"""
Cannabinoids Agent - Specialized for discovering hemp cannabinoid products
Focuses on CBD, CBG, CBN, CBC, and other non-psychoactive cannabinoids
"""

from .base_plant_part_agent import BasePlantPartAgent
from typing import List, Dict
import re
import logging

logger = logging.getLogger(__name__)


class CannabinoidsAgent(BasePlantPartAgent):
    """Agent specialized in discovering hemp cannabinoid products"""
    
    def __init__(self, supabase_client):
        super().__init__("Cannabinoids", supabase_client)
        
    def get_search_terms(self) -> List[str]:
        """Cannabinoid-specific search terms"""
        return [
            # Primary cannabinoids
            "hemp CBD products",
            "hemp CBG isolate",
            "hemp CBN products",
            "hemp CBC extract",
            "hemp CBDV products",
            "hemp THCV isolate",
            "hemp cannabinoid spectrum",
            
            # Product types
            "hemp CBD oil",
            "hemp CBD capsules",
            "hemp CBD topicals",
            "hemp CBD edibles",
            "hemp CBD vape",
            "hemp CBD tincture",
            "hemp CBD patches",
            
            # Specialized formulations
            "broad spectrum hemp CBD",
            "full spectrum hemp extract",
            "hemp CBD isolate powder",
            "water soluble hemp CBD",
            "nano hemp CBD",
            "liposomal hemp CBD",
            
            # Medical/therapeutic
            "hemp CBD pain relief",
            "hemp CBD anxiety",
            "hemp CBD sleep",
            "hemp CBD inflammation",
            "hemp CBD epilepsy",
            "hemp CBD arthritis",
            
            # Novel cannabinoids
            "hemp minor cannabinoids",
            "hemp acidic cannabinoids",
            "hemp CBDA products",
            "hemp CBGA products",
            "hemp cannabinoid blends",
            
            # Industrial/research
            "hemp cannabinoid API",
            "hemp CBD pharmaceutical",
            "hemp cannabinoid research",
            "synthetic hemp cannabinoids",
            "hemp cannabinoid standards"
        ]
        
    def get_trusted_sources(self) -> List[str]:
        """Sources specializing in cannabinoid products"""
        return [
            # Industry sources
            "projectcbd.org",
            "cbdoilreview.org",
            "leafreport.com",
            "cbdnerds.com",
            "cannabisbusinesstimes.com",
            
            # Scientific sources
            "ncbi.nlm.nih.gov",
            "cannabinoidmedicine.org",
            "icrs.co",  # International Cannabinoid Research Society
            "iacm.org",  # International Association for Cannabinoid Medicines
            
            # Trade publications
            "hempgrower.com",
            "hempindustrydaily.com",
            "cannabisindustryjournal.com",
            "extraction-magazine.com",
            
            # Regulatory/standards
            "hempsupporter.com",
            "votehemp.com",
            "ushempauthority.org",
            "thehia.org",
            
            # Market/business
            "brightfieldgroup.com",
            "bdsanalytics.com",
            "newleafdata.com"
        ]
        
    def get_industry_focus(self) -> List[str]:
        """Industries where cannabinoids are most relevant"""
        return [
            "Pharmaceuticals",
            "Health & Wellness",
            "Cosmetics & Personal Care",
            "Food & Beverage",
            "Pet Products",
            "Sports & Recovery",
            "Nutraceuticals",
            "Medical Devices"
        ]
    
    def extract_product_specific_data(self, text: str, url: str) -> Dict:
        """Extract cannabinoid-specific attributes"""
        data = {
            'cannabinoid_profile': self._extract_cannabinoid_profile(text),
            'concentration': self._extract_concentration(text),
            'extraction_method': self._extract_extraction_method(text),
            'spectrum_type': self._extract_spectrum_type(text),
            'bioavailability': self._extract_bioavailability(text),
            'third_party_tested': self._check_third_party_testing(text),
            'therapeutic_uses': self._extract_therapeutic_uses(text),
            'delivery_method': self._extract_delivery_method(text)
        }
        
        # Build benefits from extracted data
        benefits = []
        
        if data['cannabinoid_profile']:
            benefits.append(f"Contains: {', '.join(data['cannabinoid_profile'][:3])}")
            
        if data['concentration']:
            benefits.append(f"Potency: {data['concentration']}")
            
        if data['spectrum_type']:
            benefits.append(data['spectrum_type'])
            
        if data['bioavailability']:
            benefits.append(f"Enhanced bioavailability: {data['bioavailability']}")
            
        if data['third_party_tested']:
            benefits.append("Third-party tested")
            
        if data['therapeutic_uses']:
            benefits.append(f"For: {', '.join(data['therapeutic_uses'][:3])}")
            
        data['benefits_advantages'] = benefits
        
        return data
    
    def _extract_cannabinoid_profile(self, text: str) -> List[str]:
        """Extract specific cannabinoids mentioned"""
        cannabinoids = []
        
        # Common cannabinoids in hemp
        cannabinoid_names = [
            'CBD', 'cannabidiol',
            'CBG', 'cannabigerol',
            'CBN', 'cannabinol',
            'CBC', 'cannabichromene',
            'CBDV', 'cannabidivarin',
            'THCV', 'tetrahydrocannabivarin',
            'CBDA', 'cannabidiolic acid',
            'CBGA', 'cannabigerolic acid',
            'CBL', 'cannabicyclol',
            'CBE', 'cannabielsoin'
        ]
        
        text_upper = text.upper()
        for cannabinoid in cannabinoid_names:
            if cannabinoid.upper() in text_upper:
                # Normalize to standard abbreviation
                if len(cannabinoid) > 4:  # Full name
                    # Convert to abbreviation
                    abbrev_map = {
                        'CANNABIDIOL': 'CBD',
                        'CANNABIGEROL': 'CBG',
                        'CANNABINOL': 'CBN',
                        'CANNABICHROMENE': 'CBC',
                        'CANNABIDIVARIN': 'CBDV',
                        'TETRAHYDROCANNABIVARIN': 'THCV',
                        'CANNABIDIOLIC ACID': 'CBDA',
                        'CANNABIGEROLIC ACID': 'CBGA',
                        'CANNABICYCLOL': 'CBL',
                        'CANNABIELSOIN': 'CBE'
                    }
                    cannabinoids.append(abbrev_map.get(cannabinoid.upper(), cannabinoid))
                else:
                    cannabinoids.append(cannabinoid.upper())
                
        return list(set(cannabinoids))
    
    def _extract_concentration(self, text: str) -> str:
        """Extract concentration/potency information"""
        patterns = [
            r'(\d+(?:\.\d+)?)\s*mg\s*(?:of\s+)?CBD',
            r'(\d+(?:\.\d+)?)\s*mg/ml',
            r'(\d+(?:\.\d+)?)\s*%\s*CBD',
            r'(\d+(?:\.\d+)?)\s*mg\s+per\s+serving',
            r'total\s+cannabinoids[:\s]+(\d+(?:\.\d+)?)\s*mg'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
                
        return ""
    
    def _extract_extraction_method(self, text: str) -> str:
        """Extract extraction method"""
        methods = {
            'co2 extract': 'CO2 Extraction',
            'supercritical co2': 'Supercritical CO2',
            'ethanol extract': 'Ethanol Extraction',
            'cold press': 'Cold Pressed',
            'solventless': 'Solventless',
            'rosin': 'Rosin Press',
            'distillate': 'Distillation',
            'chromatography': 'Chromatography'
        }
        
        text_lower = text.lower()
        for key, value in methods.items():
            if key in text_lower:
                return value
                
        return ""
    
    def _extract_spectrum_type(self, text: str) -> str:
        """Extract spectrum type (full, broad, isolate)"""
        text_lower = text.lower()
        
        if 'isolate' in text_lower or 'pure cbd' in text_lower:
            return "CBD Isolate"
        elif 'broad spectrum' in text_lower or 'broad-spectrum' in text_lower:
            return "Broad Spectrum"
        elif 'full spectrum' in text_lower or 'full-spectrum' in text_lower:
            return "Full Spectrum"
        elif 'whole plant' in text_lower:
            return "Whole Plant Extract"
            
        return ""
    
    def _extract_bioavailability(self, text: str) -> str:
        """Extract bioavailability enhancements"""
        bio_keywords = {
            'nano': 'Nano-enhanced',
            'liposomal': 'Liposomal delivery',
            'water soluble': 'Water-soluble',
            'micelle': 'Micellar technology',
            'emulsif': 'Emulsified',
            'bioavailab': 'Enhanced bioavailability'
        }
        
        text_lower = text.lower()
        for key, value in bio_keywords.items():
            if key in text_lower:
                return value
                
        return ""
    
    def _check_third_party_testing(self, text: str) -> bool:
        """Check if product mentions third-party testing"""
        test_keywords = [
            'third party test', 'third-party test', '3rd party',
            'lab test', 'coa', 'certificate of analysis',
            'independently tested', 'verified', 'lab report'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in test_keywords)
    
    def _extract_therapeutic_uses(self, text: str) -> List[str]:
        """Extract therapeutic applications"""
        uses = []
        
        therapeutic_terms = [
            'pain relief', 'chronic pain', 'neuropathic pain',
            'anxiety', 'stress', 'depression',
            'insomnia', 'sleep', 'sleep disorders',
            'inflammation', 'anti-inflammatory',
            'epilepsy', 'seizures', 'dravet syndrome',
            'arthritis', 'joint pain', 'rheumatoid',
            'nausea', 'appetite', 'chemotherapy',
            'ptsd', 'trauma', 'mental health',
            'focus', 'adhd', 'concentration',
            'recovery', 'muscle', 'athletic'
        ]
        
        text_lower = text.lower()
        for term in therapeutic_terms:
            if term in text_lower:
                uses.append(term.title())
                
        return list(set(uses))[:5]
    
    def _extract_delivery_method(self, text: str) -> str:
        """Extract product delivery method"""
        methods = {
            'tincture': 'Sublingual Tincture',
            'sublingual': 'Sublingual',
            'capsule': 'Capsules',
            'softgel': 'Softgels',
            'gummies': 'Gummies',
            'edible': 'Edibles',
            'topical': 'Topical',
            'cream': 'Topical Cream',
            'balm': 'Balm',
            'patch': 'Transdermal Patch',
            'vape': 'Vaporizer',
            'inhale': 'Inhalation',
            'suppository': 'Suppository'
        }
        
        text_lower = text.lower()
        for key, value in methods.items():
            if key in text_lower:
                return value
                
        return ""