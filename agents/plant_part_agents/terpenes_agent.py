"""
Terpenes Agent - Specialized for discovering hemp terpene products
Focuses on aromatic compounds, flavoring, therapeutic applications
"""

from .base_plant_part_agent import BasePlantPartAgent
from typing import List, Dict
import re
import logging

logger = logging.getLogger(__name__)


class TerpenesAgent(BasePlantPartAgent):
    """Agent specialized in discovering hemp terpene products"""
    
    def __init__(self, supabase_client):
        super().__init__("Terpenes", supabase_client)
        
    def get_search_terms(self) -> List[str]:
        """Terpene-specific search terms"""
        return [
            # Primary terpenes
            "hemp terpenes",
            "cannabis terpenes extraction",
            "hemp terpene profiles",
            "terpene isolates hemp",
            "hemp essential oil terpenes",
            
            # Specific terpenes found in hemp
            "hemp limonene",
            "hemp myrcene", 
            "hemp pinene",
            "hemp linalool",
            "hemp caryophyllene",
            "hemp humulene",
            "hemp terpinolene",
            
            # Applications
            "hemp terpenes aromatherapy",
            "hemp terpenes flavoring",
            "hemp terpenes perfume",
            "hemp terpenes cosmetics",
            "hemp terpenes vape",
            "hemp terpenes edibles",
            "hemp terpenes beverages",
            
            # Industrial/commercial
            "hemp terpene blends",
            "hemp derived terpenes",
            "organic hemp terpenes",
            "hemp terpene formulations",
            "hemp terpenes wholesale",
            "hemp terpene spray",
            
            # Therapeutic
            "hemp terpenes therapeutic",
            "hemp terpenes entourage effect",
            "hemp terpenes wellness",
            "hemp terpenes relaxation",
            "hemp terpenes focus"
        ]
        
    def get_trusted_sources(self) -> List[str]:
        """Sources specializing in terpenes and aromatics"""
        return [
            # Terpene companies
            "trueterpenes.com",
            "floraplex.com", 
            "terpenecentral.com",
            "abstraxtech.com",
            "terpenelab.com",
            
            # Hemp/Cannabis industry
            "leafly.com",
            "weedmaps.com",
            "analyticalcannabis.com",
            "hempgrower.com",
            "cannabisbusinesstimes.com",
            
            # Essential oils & aromatherapy
            "aromaweb.com",
            "tisserandinstitute.org",
            "naha.org",
            
            # Scientific/research
            "ncbi.nlm.nih.gov",
            "sciencedirect.com",
            "phytochem.org",
            
            # Industry news
            "terpeneinfo.com",
            "terpenes.com",
            "cannabis-tech.com"
        ]
        
    def get_industry_focus(self) -> List[str]:
        """Industries where terpenes are most relevant"""
        return [
            "Food & Beverage",
            "Cosmetics & Personal Care", 
            "Health & Wellness",
            "Pharmaceuticals",
            "Aromatherapy",
            "Pet Products",
            "Cleaning Products",
            "Vaping & Smoking",
            "Nutraceuticals"
        ]
    
    def extract_product_specific_data(self, text: str, url: str) -> Dict:
        """Extract terpene-specific attributes"""
        data = {
            'terpene_profile': self._extract_terpene_profile(text),
            'concentration': self._extract_concentration(text),
            'extraction_method': self._extract_extraction_method(text),
            'aroma_notes': self._extract_aroma_notes(text),
            'effects': self._extract_effects(text),
            'applications': self._extract_applications(text),
            'purity': self._extract_purity(text),
            'source': self._extract_source_info(text)
        }
        
        # Build benefits from extracted data
        benefits = []
        
        if data['aroma_notes']:
            benefits.append(f"Aroma profile: {', '.join(data['aroma_notes'])}")
            
        if data['effects']:
            benefits.append(f"Effects: {', '.join(data['effects'])}")
            
        if data['concentration']:
            benefits.append(f"Concentration: {data['concentration']}")
            
        if data['extraction_method']:
            benefits.append(f"Extracted via {data['extraction_method']}")
            
        if data['purity']:
            benefits.append(f"Purity: {data['purity']}")
            
        data['benefits_advantages'] = benefits
        
        return data
    
    def _extract_terpene_profile(self, text: str) -> List[str]:
        """Extract specific terpenes mentioned"""
        terpenes = []
        
        # Common hemp terpenes
        terpene_names = [
            'limonene', 'myrcene', 'pinene', 'alpha-pinene', 'beta-pinene',
            'linalool', 'caryophyllene', 'beta-caryophyllene', 'humulene',
            'terpinolene', 'ocimene', 'nerolidol', 'bisabolol', 'guaiol',
            'eucalyptol', 'terpineol', 'borneol', 'camphene', 'geraniol'
        ]
        
        text_lower = text.lower()
        for terpene in terpene_names:
            if terpene in text_lower:
                terpenes.append(terpene.capitalize())
                
        return list(set(terpenes))
    
    def _extract_concentration(self, text: str) -> str:
        """Extract concentration information"""
        patterns = [
            r'(\d+(?:\.\d+)?)\s*(?:%|percent|mg/ml|mg/g)',
            r'concentration[:\s]+(\d+(?:\.\d+)?)\s*(?:%|percent)',
            r'(\d+(?:\.\d+)?)\s*ppm',
            r'potency[:\s]+(\d+(?:\.\d+)?)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(0)
                
        return ""
    
    def _extract_extraction_method(self, text: str) -> str:
        """Extract extraction method"""
        methods = {
            'steam distill': 'Steam Distillation',
            'co2 extract': 'CO2 Extraction',
            'supercritical': 'Supercritical CO2',
            'cold press': 'Cold Pressed',
            'solvent': 'Solvent Extraction',
            'hydro distill': 'Hydro Distillation',
            'molecular distill': 'Molecular Distillation'
        }
        
        text_lower = text.lower()
        for key, value in methods.items():
            if key in text_lower:
                return value
                
        return ""
    
    def _extract_aroma_notes(self, text: str) -> List[str]:
        """Extract aroma/flavor descriptions"""
        aromas = []
        
        # Common terpene aromas
        aroma_terms = [
            'citrus', 'lemon', 'orange', 'lime', 'grapefruit',
            'pine', 'woody', 'earthy', 'herbal', 'floral',
            'sweet', 'fruity', 'berry', 'tropical', 'mango',
            'spicy', 'pepper', 'clove', 'cinnamon',
            'minty', 'cooling', 'fresh', 'clean',
            'musky', 'diesel', 'skunky', 'pungent'
        ]
        
        text_lower = text.lower()
        for aroma in aroma_terms:
            if aroma in text_lower:
                aromas.append(aroma)
                
        return list(set(aromas))[:5]  # Limit to 5 most relevant
    
    def _extract_effects(self, text: str) -> List[str]:
        """Extract reported effects"""
        effects = []
        
        effect_terms = [
            'relaxing', 'calming', 'stress relief', 'anxiety',
            'energizing', 'uplifting', 'focus', 'clarity',
            'anti-inflammatory', 'pain relief', 'analgesic',
            'antibacterial', 'antifungal', 'antimicrobial',
            'mood enhancing', 'euphoric', 'creativity',
            'sedative', 'sleep', 'appetite', 'nausea'
        ]
        
        text_lower = text.lower()
        for effect in effect_terms:
            if effect in text_lower:
                effects.append(effect.replace('_', ' ').title())
                
        return list(set(effects))[:5]
    
    def _extract_applications(self, text: str) -> List[str]:
        """Extract product applications"""
        applications = []
        
        app_terms = [
            'vape', 'vaping', 'e-liquid',
            'edible', 'food', 'beverage', 'drink',
            'topical', 'cream', 'lotion', 'cosmetic',
            'aromatherapy', 'diffuser', 'essential oil',
            'tincture', 'sublingual', 'oral',
            'capsule', 'supplement', 'nutraceutical',
            'perfume', 'fragrance', 'scent'
        ]
        
        text_lower = text.lower()
        for app in app_terms:
            if app in text_lower:
                applications.append(app.title())
                
        return list(set(applications))[:5]
    
    def _extract_purity(self, text: str) -> str:
        """Extract purity information"""
        patterns = [
            r'(\d+(?:\.\d+)?)\s*%\s*pure',
            r'purity[:\s]+(\d+(?:\.\d+)?)\s*%',
            r'pure\s+(\d+(?:\.\d+)?)\s*%',
            r'>(\d+(?:\.\d+)?)\s*%\s*terpenes'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.lower())
            if match:
                return f"{match.group(1)}% pure"
                
        return ""
    
    def _extract_source_info(self, text: str) -> str:
        """Extract source information (hemp variety, origin)"""
        sources = []
        
        # Hemp varieties
        varieties = ['industrial hemp', 'hemp', 'cannabis sativa', 'EU approved', 'organic hemp']
        
        # Origins
        origins = ['USA', 'Colorado', 'Oregon', 'Kentucky', 'Europe', 'Canada']
        
        text_lower = text.lower()
        
        for variety in varieties:
            if variety in text_lower:
                sources.append(variety.title())
                break
                
        for origin in origins:
            if origin.lower() in text_lower:
                sources.append(f"from {origin}")
                break
                
        return ' '.join(sources)