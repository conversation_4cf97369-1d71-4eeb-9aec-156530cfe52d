"""
Hemp Roots Agent - Specialized for discovering hemp root products
Focuses on medicinal, cosmetic, and agricultural applications
"""

from .base_plant_part_agent import BasePlantPartAgent
from typing import List, Dict
import re
import logging

logger = logging.getLogger(__name__)


class RootsAgent(BasePlantPartAgent):
    """Agent specialized in discovering hemp root products"""
    
    def __init__(self, supabase_client):
        super().__init__("Hemp Roots", supabase_client)
        
    def get_search_terms(self) -> List[str]:
        """Root-specific search terms"""
        return [
            # Primary searches
            "hemp root products",
            "hemp root extract",
            "hemp root powder",
            "hemp root tea",
            "hemp root medicine",
            
            # Traditional medicine
            "hemp root balm",
            "hemp root salve",
            "hemp root tincture",
            "hemp root oil",
            "hemp root cream",
            
            # Specific compounds
            "hemp root friedelin",
            "hemp root pentacyclic triterpene",
            "hemp root ketones",
            "hemp root alkaloids",
            
            # Agricultural uses
            "hemp root biostimulant",
            "hemp root compost",
            "hemp root mulch",
            "hemp root soil amendment",
            
            # Industrial/commercial
            "hemp root fiber",
            "hemp root biomass",
            "hemp root extract wholesale",
            "organic hemp root",
            
            # Therapeutic applications
            "hemp root anti-inflammatory",
            "hemp root pain relief",
            "hemp root arthritis",
            "hemp root topical",
            "hemp root wellness"
        ]
        
    def get_trusted_sources(self) -> List[str]:
        """Sources specializing in root products and traditional medicine"""
        return [
            # Hemp industry
            "hempgazette.com",
            "hemp.com",
            "ministryofhemp.com",
            "hempsupporter.com",
            
            # Natural health
            "naturalnews.com",
            "herbalgram.org",
            "botanical.com",
            "herbs.org",
            
            # Research
            "ncbi.nlm.nih.gov",
            "sciencedirect.com",
            "researchgate.net",
            
            # Traditional medicine
            "tcmworld.org",
            "ayurveda.com",
            "herbalists.org.uk",
            
            # Agriculture
            "agriculture.com",
            "agweb.com",
            "farmersweekly.co.za"
        ]
        
    def get_industry_focus(self) -> List[str]:
        """Industries where roots are most relevant"""
        return [
            "Health & Wellness",
            "Cosmetics & Personal Care",
            "Pharmaceuticals",
            "Agriculture",
            "Traditional Medicine",
            "Pet Products",
            "Nutraceuticals"
        ]
    
    def extract_product_specific_data(self, text: str, url: str) -> Dict:
        """Extract root-specific attributes"""
        data = {
            'active_compounds': self._extract_compounds(text),
            'extraction_method': self._extract_extraction_method(text),
            'therapeutic_uses': self._extract_therapeutic_uses(text),
            'preparation_method': self._extract_preparation(text),
            'potency': self._extract_potency(text),
            'traditional_use': self._extract_traditional_use(text)
        }
        
        # Build benefits from extracted data
        benefits = []
        
        if data['active_compounds']:
            benefits.append(f"Contains: {', '.join(data['active_compounds'][:3])}")
            
        if data['therapeutic_uses']:
            benefits.append(f"Used for: {', '.join(data['therapeutic_uses'][:3])}")
            
        if data['extraction_method']:
            benefits.append(f"Extracted via {data['extraction_method']}")
            
        if data['traditional_use']:
            benefits.append("Traditional medicine use")
            
        if 'organic' in text.lower():
            benefits.append("Organic certified")
            
        data['benefits_advantages'] = benefits
        
        return data
    
    def _extract_compounds(self, text: str) -> List[str]:
        """Extract active compounds mentioned"""
        compounds = []
        
        # Known hemp root compounds
        compound_names = [
            'friedelin', 'epifriedelanol', 'pentacyclic triterpene',
            'alkaloid', 'choline', 'atropine', 'piperidine',
            'pyrrolidine', 'sterols', 'terpenes', 'flavonoids',
            'cannabinoids', 'phenolic', 'ketones'
        ]
        
        text_lower = text.lower()
        for compound in compound_names:
            if compound in text_lower:
                compounds.append(compound.capitalize())
                
        return list(set(compounds))
    
    def _extract_extraction_method(self, text: str) -> str:
        """Extract extraction method"""
        methods = {
            'ethanol extract': 'Ethanol Extraction',
            'alcohol extract': 'Alcohol Extraction',
            'water extract': 'Water Extraction',
            'decoction': 'Decoction',
            'cold press': 'Cold Pressed',
            'supercritical': 'Supercritical CO2',
            'traditional': 'Traditional Method'
        }
        
        text_lower = text.lower()
        for key, value in methods.items():
            if key in text_lower:
                return value
                
        return ""
    
    def _extract_therapeutic_uses(self, text: str) -> List[str]:
        """Extract therapeutic applications"""
        uses = []
        
        therapeutic_terms = [
            'anti-inflammatory', 'pain relief', 'arthritis', 'joint pain',
            'muscle ache', 'gout', 'fever', 'infection', 'wound healing',
            'skin condition', 'eczema', 'psoriasis', 'burns', 'bruises',
            'hemorrhoids', 'digestive', 'liver health', 'detox'
        ]
        
        text_lower = text.lower()
        for term in therapeutic_terms:
            if term in text_lower:
                uses.append(term.replace('-', ' ').title())
                
        return list(set(uses))[:5]
    
    def _extract_preparation(self, text: str) -> str:
        """Extract preparation method"""
        preparations = {
            'tea': 'Tea/Infusion',
            'infusion': 'Tea/Infusion',
            'tincture': 'Tincture',
            'salve': 'Salve/Balm',
            'balm': 'Salve/Balm',
            'powder': 'Powder',
            'capsule': 'Capsule',
            'topical': 'Topical Application',
            'poultice': 'Poultice'
        }
        
        text_lower = text.lower()
        for key, value in preparations.items():
            if key in text_lower:
                return value
                
        return ""
    
    def _extract_potency(self, text: str) -> str:
        """Extract potency information"""
        patterns = [
            r'(\d+(?:\.\d+)?)\s*mg/ml',
            r'(\d+(?:\.\d+)?)\s*mg\s+per',
            r'(\d+(?:\.\d+)?)\s*%\s+extract',
            r'(\d+):(\d+)\s+ratio'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(0)
                
        return ""
    
    def _extract_traditional_use(self, text: str) -> bool:
        """Check if traditional/historical use is mentioned"""
        traditional_keywords = [
            'traditional', 'ancient', 'historical', 'folk medicine',
            'chinese medicine', 'tcm', 'ayurveda', 'native american',
            'centuries', 'historically'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in traditional_keywords)