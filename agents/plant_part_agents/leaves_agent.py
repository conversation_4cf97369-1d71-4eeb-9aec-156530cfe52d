"""
Hemp Leaves Agent - Specialized for discovering hemp leaf products
Focuses on nutritional, medicinal, cosmetic, and agricultural applications
"""

from .base_plant_part_agent import BasePlantPartAgent
from typing import List, Dict
import re
import logging

logger = logging.getLogger(__name__)


class LeavesAgent(BasePlantPartAgent):
    """Agent specialized in discovering hemp leaf products"""
    
    def __init__(self, supabase_client):
        super().__init__("Hemp Leaves", supabase_client)
        
    def get_search_terms(self) -> List[str]:
        """Leaf-specific search terms"""
        return [
            # Primary searches
            "hemp leaf products",
            "hemp leaf extract",
            "hemp leaf powder",
            "hemp leaf tea",
            "hemp leaves juice",
            
            # Nutritional products
            "hemp leaf smoothie",
            "hemp leaf salad",
            "hemp leaf supplements",
            "raw hemp leaves",
            "hemp leaf greens",
            
            # Traditional uses
            "hemp leaf poultice",
            "hemp leaf compress",
            "hemp leaf infusion",
            "hemp leaf tonic",
            
            # Cosmetic applications
            "hemp leaf skincare",
            "hemp leaf face mask",
            "hemp leaf lotion",
            "hemp leaf serum",
            
            # Agricultural uses
            "hemp leaf mulch",
            "hemp leaf compost",
            "hemp leaf animal feed",
            "hemp leaf silage",
            
            # Industrial/commercial
            "hemp leaf extract wholesale",
            "organic hemp leaves",
            "hemp leaf biomass",
            "hemp leaf concentrate",
            
            # Health applications
            "hemp leaf antioxidants",
            "hemp leaf chlorophyll",
            "hemp leaf vitamins",
            "hemp leaf minerals",
            "hemp leaf phytonutrients"
        ]
        
    def get_trusted_sources(self) -> List[str]:
        """Sources specializing in leaf products and nutrition"""
        return [
            # Hemp industry
            "leafly.com",
            "hemp.com",
            "hempfoundation.net",
            "thehia.org",
            
            # Nutrition and health
            "nutritiondata.self.com",
            "healthline.com",
            "medicalnewstoday.com",
            "nutraceuticalsworld.com",
            
            # Natural products
            "naturalnews.com",
            "organicfacts.net",
            "superfoodly.com",
            
            # Agriculture
            "agdaily.com",
            "feedstuffs.com",
            "progressivefarmer.com",
            
            # Research
            "ncbi.nlm.nih.gov",
            "sciencedirect.com",
            "journals.plos.org"
        ]
        
    def get_industry_focus(self) -> List[str]:
        """Industries where leaves are most relevant"""
        return [
            "Food & Beverage",
            "Health & Wellness",
            "Cosmetics & Personal Care",
            "Agriculture",
            "Pet Products",
            "Nutraceuticals",
            "Pharmaceuticals"
        ]
    
    def extract_product_specific_data(self, text: str, url: str) -> Dict:
        """Extract leaf-specific attributes"""
        data = {
            'nutrients': self._extract_nutrients(text),
            'preparation_method': self._extract_preparation(text),
            'health_benefits': self._extract_health_benefits(text),
            'chlorophyll_content': self._extract_chlorophyll(text),
            'freshness': self._extract_freshness(text),
            'organic_status': self._check_organic(text)
        }
        
        # Build benefits from extracted data
        benefits = []
        
        if data['nutrients']:
            benefits.append(f"Rich in: {', '.join(data['nutrients'][:3])}")
            
        if data['health_benefits']:
            benefits.append(f"Supports: {', '.join(data['health_benefits'][:3])}")
            
        if data['chlorophyll_content']:
            benefits.append("High chlorophyll content")
            
        if data['organic_status']:
            benefits.append("Certified organic")
            
        if data['freshness']:
            benefits.append(data['freshness'])
            
        data['benefits_advantages'] = benefits
        
        return data
    
    def _extract_nutrients(self, text: str) -> List[str]:
        """Extract nutrients mentioned"""
        nutrients = []
        
        # Common nutrients in hemp leaves
        nutrient_names = [
            'protein', 'fiber', 'omega-3', 'omega-6', 'vitamin a',
            'vitamin c', 'vitamin e', 'vitamin k', 'calcium', 'iron',
            'magnesium', 'phosphorus', 'potassium', 'zinc', 'folate',
            'chlorophyll', 'antioxidants', 'flavonoids', 'polyphenols',
            'amino acids', 'essential fatty acids'
        ]
        
        text_lower = text.lower()
        for nutrient in nutrient_names:
            if nutrient in text_lower:
                nutrients.append(nutrient.title())
                
        return list(set(nutrients))
    
    def _extract_preparation(self, text: str) -> str:
        """Extract preparation method"""
        preparations = {
            'juice': 'Fresh Juice',
            'juiced': 'Fresh Juice',
            'powder': 'Dried Powder',
            'freeze dried': 'Freeze Dried',
            'fresh': 'Fresh/Raw',
            'raw': 'Fresh/Raw',
            'dried': 'Air Dried',
            'extract': 'Concentrated Extract',
            'infusion': 'Infusion/Tea',
            'fermented': 'Fermented'
        }
        
        text_lower = text.lower()
        for key, value in preparations.items():
            if key in text_lower:
                return value
                
        return ""
    
    def _extract_health_benefits(self, text: str) -> List[str]:
        """Extract health benefits"""
        benefits = []
        
        benefit_terms = [
            'anti-inflammatory', 'antioxidant', 'immune support',
            'digestive health', 'detoxification', 'alkalizing',
            'energy boost', 'metabolism', 'weight management',
            'skin health', 'anti-aging', 'cardiovascular',
            'blood sugar', 'cholesterol', 'liver health'
        ]
        
        text_lower = text.lower()
        for term in benefit_terms:
            if term in text_lower:
                benefits.append(term.replace('-', ' ').title())
                
        return list(set(benefits))[:5]
    
    def _extract_chlorophyll(self, text: str) -> bool:
        """Check if chlorophyll content is mentioned"""
        chlorophyll_keywords = [
            'chlorophyll', 'green pigment', 'photosynthesis',
            'detoxifying', 'blood builder', 'oxygen'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in chlorophyll_keywords)
    
    def _extract_freshness(self, text: str) -> str:
        """Extract freshness information"""
        freshness_indicators = {
            'fresh': 'Fresh leaves',
            'freshly harvest': 'Freshly harvested',
            'freeze dried': 'Freeze dried to preserve nutrients',
            'flash frozen': 'Flash frozen',
            'within 24 hour': 'Processed within 24 hours',
            'raw': 'Raw unprocessed'
        }
        
        text_lower = text.lower()
        for key, value in freshness_indicators.items():
            if key in text_lower:
                return value
                
        return ""
    
    def _check_organic(self, text: str) -> bool:
        """Check if product is organic"""
        organic_keywords = [
            'organic', 'certified organic', 'usda organic',
            'pesticide free', 'chemical free', 'non-gmo'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in organic_keywords)