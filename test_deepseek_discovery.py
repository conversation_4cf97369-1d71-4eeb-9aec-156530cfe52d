#!/usr/bin/env python3
"""
Test AI-powered product discovery using DeepSeek
Alternative to OpenAI for product discovery
"""

import os
import json
import logging
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def discover_with_deepseek(plant_part: str, count: int = 5):
    """Use DeepSeek AI to discover hemp products"""
    
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        logger.error("No DeepSeek API key found")
        return None
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    prompt = f"""You are a hemp industry expert. Generate {count} innovative hemp {plant_part} products.

For each product provide this exact JSON structure:
[
  {{
    "name": "Product Name",
    "description": "Brief description",
    "industry": "Industry Category",
    "benefits": ["Benefit 1", "Benefit 2", "Benefit 3"],
    "technical_specs": "Key technical details"
  }}
]

Focus on real, cutting-edge products that exist in the market."""

    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": "You are a hemp industry expert. Respond only with valid JSON."},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": 800
    }
    
    try:
        response = requests.post(
            "https://api.deepseek.com/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            # Try to parse as JSON
            try:
                products = json.loads(content)
                return products
            except:
                # If not valid JSON, return raw content
                return content
        else:
            logger.error(f"DeepSeek API error: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"DeepSeek request error: {e}")
        return None


def test_deepseek_discovery():
    """Test product discovery with DeepSeek"""
    
    print("\n" + "="*60)
    print("🤖 DEEPSEEK AI PRODUCT DISCOVERY TEST")
    print("="*60)
    
    # Test different plant parts
    test_cases = [
        ("Cannabinoids", 3),
        ("Hemp Fiber", 3),
        ("Terpenes", 3)
    ]
    
    discovered_products = []
    
    for plant_part, count in test_cases:
        print(f"\n\n🌿 Discovering {plant_part} Products...")
        print("-" * 40)
        
        products = discover_with_deepseek(plant_part, count)
        
        if products:
            print("\n✅ Discovery successful!")
            
            if isinstance(products, list):
                print(f"Found {len(products)} products:")
                for i, product in enumerate(products, 1):
                    print(f"\n{i}. {product.get('name', 'Unknown')}")
                    print(f"   Industry: {product.get('industry', 'N/A')}")
                    print(f"   Description: {product.get('description', 'N/A')}")
                    if 'benefits' in product:
                        print(f"   Benefits: {', '.join(product['benefits'])}")
                
                discovered_products.extend(products)
            else:
                print(products)
        else:
            print("\n❌ Discovery failed")
    
    return discovered_products


def test_innovation_search():
    """Search for innovative hemp applications"""
    
    print("\n\n🚀 INNOVATION SEARCH WITH DEEPSEEK")
    print("="*60)
    
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        print("❌ No DeepSeek API key found")
        return
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [
            {
                "role": "user",
                "content": """List 5 cutting-edge hemp innovations from recent years:
1. Hemp-based graphene or supercapacitors
2. Hemp quantum dots for electronics
3. Hemp materials for space/aerospace
4. Hemp bioprinting or tissue engineering
5. Hemp-based semiconductors or computing

For each, provide the innovation name and a brief technical description."""
            }
        ],
        "temperature": 0.8,
        "max_tokens": 600
    }
    
    try:
        response = requests.post(
            "https://api.deepseek.com/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print("\n" + content)
            return True
        else:
            print(f"\n❌ API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"\n❌ Request error: {e}")
        return False


def main():
    """Run discovery tests"""
    
    # Test product discovery
    products = test_deepseek_discovery()
    
    # Test innovation search
    test_innovation_search()
    
    # Summary
    print("\n\n" + "="*60)
    print("📊 DISCOVERY SUMMARY")
    print("="*60)
    
    if products:
        print(f"\nTotal products discovered: {len(products)}")
        print("\nNext steps:")
        print("1. ✅ AI discovery is working with DeepSeek")
        print("2. 📝 Parse and validate these products")
        print("3. 💾 Store in database with proper attribution")
        print("4. 🔄 Set up automated discovery pipeline")
        print("5. 🌐 Add web scraping for validation")
    else:
        print("\n❌ No products discovered - check API configuration")


if __name__ == "__main__":
    main()