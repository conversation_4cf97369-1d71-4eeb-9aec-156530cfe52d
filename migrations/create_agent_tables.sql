-- Migration: Create Agent Orchestration Tables
-- Date: July 2, 2025
-- Purpose: Enable advanced agent orchestration, task management, and monitoring

-- 1. Agent Task Queue - Central task management
CREATE TABLE IF NOT EXISTS agent_task_queue (
    id BIGSERIAL PRIMARY KEY,
    task_id UUID DEFAULT gen_random_uuid() UNIQUE NOT NULL,
    agent_type TEXT NOT NULL CHECK (agent_type IN (
        'research_agent', 'content_agent', 'seo_agent', 
        'outreach_agent', 'monetization_agent', 'compliance_agent',
        'plant_part_agent', 'innovation_agent', 'sustainability_agent'
    )),
    task_type TEXT NOT NULL,
    priority TEXT NOT NULL CHECK (priority IN ('high', 'medium', 'low')) DEFAULT 'medium',
    status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
    parameters JSONB DEFAULT '{}',
    result JSONB,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    scheduled_for TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by TEXT DEFAULT 'system',
    description TEXT,
    dependencies JSONB DEFAULT '[]', -- Array of task_ids this task depends on
    CONSTRAINT valid_schedule CHECK (scheduled_for IS NULL OR scheduled_for > created_at)
);

-- 2. Agent Orchestration Logs - Detailed execution logs
CREATE TABLE IF NOT EXISTS agent_orchestration_logs (
    id BIGSERIAL PRIMARY KEY,
    task_id UUID REFERENCES agent_task_queue(task_id) ON DELETE CASCADE,
    agent_type TEXT NOT NULL,
    log_level TEXT NOT NULL CHECK (log_level IN ('debug', 'info', 'warning', 'error', 'critical')) DEFAULT 'info',
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3. Agent Performance Metrics - Track agent effectiveness
CREATE TABLE IF NOT EXISTS agent_performance_metrics (
    id BIGSERIAL PRIMARY KEY,
    agent_type TEXT NOT NULL,
    metric_name TEXT NOT NULL,
    metric_value DECIMAL,
    metric_unit TEXT,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(agent_type, metric_name, period_start, period_end)
);

-- 4. Agent Generated Content - Store agent outputs
CREATE TABLE IF NOT EXISTS agent_generated_content (
    id BIGSERIAL PRIMARY KEY,
    content_id UUID DEFAULT gen_random_uuid() UNIQUE NOT NULL,
    task_id UUID REFERENCES agent_task_queue(task_id),
    agent_type TEXT NOT NULL,
    content_type TEXT NOT NULL CHECK (content_type IN (
        'blog_post', 'product_description', 'social_media', 
        'email', 'report', 'analysis', 'product_data'
    )),
    title TEXT,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    status TEXT CHECK (status IN ('draft', 'review', 'approved', 'published', 'rejected')) DEFAULT 'draft',
    quality_score DECIMAL(3,2) CHECK (quality_score >= 0 AND quality_score <= 1),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    published_at TIMESTAMP
);

-- 5. Agent Outreach Campaigns - Track outreach efforts
CREATE TABLE IF NOT EXISTS agent_outreach_campaigns (
    id BIGSERIAL PRIMARY KEY,
    campaign_id UUID DEFAULT gen_random_uuid() UNIQUE NOT NULL,
    task_id UUID REFERENCES agent_task_queue(task_id),
    campaign_name TEXT NOT NULL,
    target_audience TEXT,
    outreach_type TEXT CHECK (outreach_type IN ('email', 'social', 'partnership', 'pr', 'other')),
    contacts JSONB DEFAULT '[]',
    status TEXT CHECK (status IN ('planning', 'active', 'paused', 'completed', 'cancelled')) DEFAULT 'planning',
    metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    started_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- 6. Agent SEO Analysis - Store SEO research results
CREATE TABLE IF NOT EXISTS agent_seo_analysis (
    id BIGSERIAL PRIMARY KEY,
    analysis_id UUID DEFAULT gen_random_uuid() UNIQUE NOT NULL,
    task_id UUID REFERENCES agent_task_queue(task_id),
    target_keyword TEXT NOT NULL,
    search_volume INTEGER,
    difficulty_score DECIMAL(3,2),
    current_ranking INTEGER,
    competitor_analysis JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 7. Agent Monetization Opportunities - Track business opportunities
CREATE TABLE IF NOT EXISTS agent_monetization_opportunities (
    id BIGSERIAL PRIMARY KEY,
    opportunity_id UUID DEFAULT gen_random_uuid() UNIQUE NOT NULL,
    task_id UUID REFERENCES agent_task_queue(task_id),
    opportunity_type TEXT NOT NULL,
    market_size DECIMAL(12,2),
    investment_required DECIMAL(12,2),
    projected_revenue DECIMAL(12,2),
    risk_assessment TEXT,
    details JSONB DEFAULT '{}',
    status TEXT CHECK (status IN ('identified', 'analyzing', 'viable', 'not_viable', 'pursued')) DEFAULT 'identified',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 8. Agent Compliance Alerts - Track regulatory issues
CREATE TABLE IF NOT EXISTS agent_compliance_alerts (
    id BIGSERIAL PRIMARY KEY,
    alert_id UUID DEFAULT gen_random_uuid() UNIQUE NOT NULL,
    task_id UUID REFERENCES agent_task_queue(task_id),
    compliance_type TEXT NOT NULL,
    severity TEXT CHECK (severity IN ('low', 'medium', 'high', 'critical')) DEFAULT 'medium',
    affected_products JSONB DEFAULT '[]',
    regulations JSONB DEFAULT '[]',
    recommendations TEXT,
    status TEXT CHECK (status IN ('open', 'investigating', 'resolved', 'ignored')) DEFAULT 'open',
    created_at TIMESTAMP DEFAULT NOW(),
    resolved_at TIMESTAMP
);

-- 9. Agent Configuration - Store agent settings
CREATE TABLE IF NOT EXISTS agent_configuration (
    id BIGSERIAL PRIMARY KEY,
    agent_type TEXT NOT NULL UNIQUE,
    config JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    rate_limit INTEGER DEFAULT 60, -- requests per minute
    max_concurrent_tasks INTEGER DEFAULT 5,
    priority_weight DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 10. Agent Dependencies - Track external service usage
CREATE TABLE IF NOT EXISTS agent_dependencies (
    id BIGSERIAL PRIMARY KEY,
    service_name TEXT NOT NULL UNIQUE,
    service_type TEXT CHECK (service_type IN ('ai_provider', 'api', 'database', 'storage', 'other')),
    status TEXT CHECK (status IN ('active', 'degraded', 'down', 'maintenance')) DEFAULT 'active',
    last_check TIMESTAMP DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_task_queue_status ON agent_task_queue(status, priority, created_at);
CREATE INDEX IF NOT EXISTS idx_task_queue_agent ON agent_task_queue(agent_type, status);
CREATE INDEX IF NOT EXISTS idx_task_queue_scheduled ON agent_task_queue(scheduled_for) WHERE scheduled_for IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_logs_task ON agent_orchestration_logs(task_id, created_at);
CREATE INDEX IF NOT EXISTS idx_logs_level ON agent_orchestration_logs(log_level) WHERE log_level IN ('error', 'critical');
CREATE INDEX IF NOT EXISTS idx_metrics_agent ON agent_performance_metrics(agent_type, period_start);
CREATE INDEX IF NOT EXISTS idx_content_status ON agent_generated_content(status, created_at);
CREATE INDEX IF NOT EXISTS idx_compliance_status ON agent_compliance_alerts(status, severity);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_agent_config_updated_at BEFORE UPDATE ON agent_configuration
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_content_updated_at BEFORE UPDATE ON agent_generated_content
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default agent configurations
INSERT INTO agent_configuration (agent_type, config) VALUES
    ('research_agent', '{"max_results": 50, "search_depth": 3, "verify_sources": true}'::jsonb),
    ('content_agent', '{"min_word_count": 500, "max_word_count": 2000, "style": "professional"}'::jsonb),
    ('seo_agent', '{"target_regions": ["US", "CA", "UK"], "keyword_limit": 100}'::jsonb),
    ('outreach_agent', '{"email_limit_per_day": 50, "follow_up_days": 3}'::jsonb),
    ('monetization_agent', '{"min_market_size": 100000, "risk_threshold": 0.7}'::jsonb),
    ('compliance_agent', '{"check_frequency_hours": 24, "jurisdictions": ["US", "EU"]}'::jsonb),
    ('plant_part_agent', '{"products_per_run": 20, "confidence_threshold": 0.7}'::jsonb),
    ('innovation_agent', '{"focus_areas": ["biotech", "materials", "energy"], "min_trl": 4}'::jsonb),
    ('sustainability_agent', '{"impact_metrics": ["carbon", "water", "waste"], "min_impact_score": 0.6}'::jsonb)
ON CONFLICT (agent_type) DO NOTHING;

-- Insert default dependencies
INSERT INTO agent_dependencies (service_name, service_type, status) VALUES
    ('OpenAI', 'ai_provider', 'active'),
    ('Anthropic Claude', 'ai_provider', 'active'),
    ('PostgreSQL', 'database', 'active'),
    ('Supabase', 'database', 'active'),
    ('Google Search API', 'api', 'active'),
    ('PubMed API', 'api', 'active')
ON CONFLICT (service_name) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE agent_task_queue IS 'Central queue for all agent tasks with dependency management';
COMMENT ON TABLE agent_orchestration_logs IS 'Detailed logs of agent execution for debugging and monitoring';
COMMENT ON TABLE agent_performance_metrics IS 'Track agent effectiveness and performance over time';
COMMENT ON TABLE agent_generated_content IS 'Store all content generated by agents';
COMMENT ON TABLE agent_configuration IS 'Agent-specific configuration and settings';

-- Grant permissions (adjust based on your users)
-- GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO agent_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO agent_user;