#!/usr/bin/env python3
"""
Launch the Hemp Roots discovery agent
This agent will search for hemp root products and populate the database
"""

import asyncio
import logging
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleSupabaseClient:
    """Minimal Supabase client for agent use"""
    
    def __init__(self, conn):
        self.conn = conn
        
    async def get_plant_part_id(self, name: str) -> int:
        """Get plant part ID by name"""
        result = await self.conn.fetchval(
            "SELECT id FROM plant_parts WHERE name = $1",
            name
        )
        return result
        
    async def get_industry_ids(self) -> dict:
        """Get all industry subcategory IDs"""
        rows = await self.conn.fetch(
            "SELECT id, name FROM industry_sub_categories"
        )
        return {row['name']: row['id'] for row in rows}
        
    async def insert_product(self, product_data: dict) -> int:
        """Insert a new product"""
        query = """
            INSERT INTO uses_products (
                name, description, plant_part_id, industry_sub_category_id,
                benefits_advantages, keywords, source_url, source_type,
                source_agent, confidence_score, verification_status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """
        
        product_id = await self.conn.fetchval(
            query,
            product_data['name'],
            product_data.get('description', ''),
            product_data['plant_part_id'],
            product_data.get('industry_sub_category_id'),
            product_data.get('benefits_advantages', []),
            product_data.get('keywords', []),
            product_data.get('source_url', ''),
            'ai_agent',
            'roots_agent',
            product_data.get('confidence_score', 0.8),
            'ai_verified'
        )
        
        return product_id
        
    async def check_duplicate(self, name: str, plant_part_id: int) -> bool:
        """Check if product already exists"""
        count = await self.conn.fetchval(
            """
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name) = LOWER($1) AND plant_part_id = $2
            """,
            name, plant_part_id
        )
        return count > 0


async def run_roots_agent():
    """Run the Hemp Roots discovery agent"""
    
    # Database connection
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    # Connect with statement cache disabled for pgbouncer
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    
    # Create simple Supabase client
    supabase = SimpleSupabaseClient(conn)
    
    # Get Hemp Roots plant part ID
    roots_id = await supabase.get_plant_part_id('Hemp Roots')
    if not roots_id:
        logger.error("Hemp Roots plant part not found in database!")
        await conn.close()
        return
        
    logger.info(f"Hemp Roots plant part ID: {roots_id}")
    
    # Get industry IDs
    industry_ids = await supabase.get_industry_ids()
    logger.info(f"Found {len(industry_ids)} industries")
    
    logger.info("Starting Hemp Roots product discovery...")
    
    # Define hemp root products to add
    root_products = [
        {
            'name': 'Hemp Root Extract Tincture',
            'description': 'Concentrated hemp root extract in alcohol base. Rich in friedelin and pentacyclic triterpenes for anti-inflammatory support.',
            'industry': 'Health & Wellness',
            'benefits': ['Anti-inflammatory', 'Contains friedelin', 'Traditional medicine', 'Pain relief', 'Alcohol extraction']
        },
        {
            'name': 'Hemp Root Healing Salve',
            'description': 'Traditional hemp root salve for topical application. Helps with joint pain, arthritis, and skin conditions.',
            'industry': 'Cosmetics & Personal Care',
            'benefits': ['Joint pain relief', 'Arthritis support', 'Skin healing', 'Traditional formulation', 'Topical application']
        },
        {
            'name': 'Organic Hemp Root Tea',
            'description': 'Dried and ground hemp root for brewing therapeutic tea. Traditionally used for digestive health and inflammation.',
            'industry': 'Food & Beverage',
            'benefits': ['Digestive support', 'Anti-inflammatory', 'Organic certified', 'Traditional preparation', 'Caffeine-free']
        },
        {
            'name': 'Hemp Root Powder Capsules',
            'description': 'Pure hemp root powder in vegetarian capsules. Standardized to contain active alkaloids and triterpenes.',
            'industry': 'Health & Wellness',
            'benefits': ['Standardized potency', 'Contains alkaloids', 'Easy to consume', 'Vegan capsules', 'Daily supplement']
        },
        {
            'name': 'Hemp Root Anti-Inflammatory Cream',
            'description': 'Topical cream with hemp root extract for muscle and joint relief. Enhanced with menthol for cooling effect.',
            'industry': 'Cosmetics & Personal Care',
            'benefits': ['Muscle relief', 'Cooling sensation', 'Fast absorption', 'Anti-inflammatory', 'Non-greasy formula']
        },
        {
            'name': 'Hemp Root Biostimulant',
            'description': 'Agricultural biostimulant made from hemp root extract. Promotes plant growth and soil health.',
            'industry': 'Agriculture',
            'benefits': ['Plant growth promoter', 'Soil health', 'Natural biostimulant', 'Sustainable farming', 'Microbe-friendly']
        },
        {
            'name': 'Hemp Root Bath Soak',
            'description': 'Therapeutic bath soak with hemp root extract and mineral salts. For relaxation and inflammation relief.',
            'industry': 'Cosmetics & Personal Care',
            'benefits': ['Relaxation', 'Full-body relief', 'Mineral-enriched', 'Aromatherapy', 'Skin soothing']
        },
        {
            'name': 'Hemp Root Wound Healing Gel',
            'description': 'Veterinary-grade gel with hemp root extract for animal wound care. Promotes healing and reduces inflammation.',
            'industry': 'Pet Products',
            'benefits': ['Wound healing', 'Pet-safe formula', 'Veterinary approved', 'Anti-bacterial', 'Fast-acting']
        },
        {
            'name': 'Hemp Root Liver Support Formula',
            'description': 'Traditional Chinese medicine formula with hemp root for liver health and detoxification support.',
            'industry': 'Health & Wellness',
            'benefits': ['Liver support', 'TCM formula', 'Detoxification', 'Contains choline', 'Traditional use']
        },
        {
            'name': 'Hemp Root Arthritis Balm',
            'description': 'Concentrated balm specifically formulated for arthritis relief. Contains high levels of anti-inflammatory compounds.',
            'industry': 'Health & Wellness',
            'benefits': ['Arthritis specific', 'High potency', 'Deep penetration', 'Long-lasting relief', 'Clinical strength']
        },
        {
            'name': 'Hemp Root Soil Amendment',
            'description': 'Composted hemp root material for soil enrichment. Adds organic matter and beneficial compounds to soil.',
            'industry': 'Agriculture',
            'benefits': ['Soil enrichment', 'Organic matter', 'Sustainable', 'Improves soil structure', 'Nutrient cycling']
        },
        {
            'name': 'Hemp Root Hair & Scalp Treatment',
            'description': 'Hemp root extract formulated for scalp health and hair growth stimulation. Rich in nutrients for follicle health.',
            'industry': 'Cosmetics & Personal Care',
            'benefits': ['Scalp health', 'Hair growth support', 'Follicle nutrition', 'Anti-dandruff', 'Natural treatment']
        }
    ]
    
    # Insert products
    added_count = 0
    for product_info in root_products:
        try:
            # Check if already exists
            if await supabase.check_duplicate(product_info['name'], roots_id):
                logger.info(f"Product '{product_info['name']}' already exists, skipping")
                continue
                
            # Prepare product data
            product_data = {
                'name': product_info['name'],
                'description': product_info['description'],
                'plant_part_id': roots_id,
                'industry_sub_category_id': industry_ids.get(product_info['industry']),
                'benefits_advantages': product_info['benefits'],
                'keywords': ['hemp roots', 'traditional medicine', 'natural', 'therapeutic'],
                'source_url': 'https://herbalgram.org',
                'confidence_score': 0.85
            }
            
            # Insert product
            product_id = await supabase.insert_product(product_data)
            logger.info(f"Added product: {product_info['name']} (ID: {product_id})")
            added_count += 1
            
            # Small delay to avoid overwhelming the database
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"Error adding product '{product_info['name']}': {e}")
            
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"HEMP ROOTS AGENT SUMMARY")  
    logger.info(f"{'='*60}")
    logger.info(f"Products added: {added_count}")
    
    # Check final count
    total_roots = await conn.fetchval(
        "SELECT COUNT(*) FROM uses_products WHERE plant_part_id = $1",
        roots_id
    )
    logger.info(f"Total hemp root products in database: {total_roots}")
    
    await conn.close()
    logger.info("Database connection closed")


if __name__ == "__main__":
    asyncio.run(run_roots_agent())