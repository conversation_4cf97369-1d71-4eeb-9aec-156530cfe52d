#!/usr/bin/env python3
"""
Create agent tasks for the orchestrator to process
This populates the agent_task_queue table with various tasks
"""

import asyncio
import logging
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def create_tasks():
    """Create various agent tasks"""
    
    # Database connection
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    # Connect with statement cache disabled for pgbouncer
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    
    # Define tasks to create
    tasks = [
        # Research tasks - Find innovative hemp products
        {
            'agent_type': 'research_agent',
            'task_type': 'discover_products',
            'priority': 'high',
            'parameters': {
                'query': 'innovative hemp bioplastics automotive industry',
                'focus': 'industrial applications',
                'plant_part': 'Bast Fiber'
            },
            'description': 'Research innovative hemp bioplastics for automotive applications'
        },
        {
            'agent_type': 'research_agent',
            'task_type': 'discover_products',
            'priority': 'high',
            'parameters': {
                'query': 'hemp graphene nanomaterials electronics',
                'focus': 'advanced materials',
                'plant_part': 'Bast Fiber'
            },
            'description': 'Discover hemp-based graphene and nanomaterials for electronics'
        },
        {
            'agent_type': 'research_agent',
            'task_type': 'discover_products',
            'priority': 'medium',
            'parameters': {
                'query': 'hemp mushroom composite packaging materials',
                'focus': 'sustainable packaging',
                'plant_part': 'Hurds'
            },
            'description': 'Research hemp-mushroom composite materials for packaging'
        },
        
        # Content generation tasks
        {
            'agent_type': 'content_agent',
            'task_type': 'generate_blog_post',
            'priority': 'medium',
            'parameters': {
                'topic': 'The Future of Hemp in Space Technology',
                'keywords': ['hemp aerospace', 'space materials', 'radiation shielding'],
                'word_count': 1500
            },
            'description': 'Write blog post about hemp applications in space technology'
        },
        {
            'agent_type': 'content_agent',
            'task_type': 'generate_product_description',
            'priority': 'low',
            'parameters': {
                'product_id': 1386,  # Hemp Hurd Acoustic Panels
                'style': 'technical',
                'length': 'detailed'
            },
            'description': 'Create detailed technical description for Hemp Hurd Acoustic Panels'
        },
        
        # SEO tasks
        {
            'agent_type': 'seo_agent',
            'task_type': 'research_keywords',
            'priority': 'medium',
            'parameters': {
                'topic': 'hemp construction materials',
                'target_audience': 'architects and builders',
                'geo_target': 'USA'
            },
            'description': 'Research SEO keywords for hemp construction materials'
        },
        
        # Outreach tasks
        {
            'agent_type': 'outreach_agent',
            'task_type': 'find_partnerships',
            'priority': 'low',
            'parameters': {
                'industry': 'sustainable fashion',
                'company_size': 'startup',
                'location': 'Europe'
            },
            'description': 'Find sustainable fashion startups in Europe for partnerships'
        },
        
        # Monetization tasks
        {
            'agent_type': 'monetization_agent',
            'task_type': 'analyze_opportunities',
            'priority': 'medium',
            'parameters': {
                'market': 'hemp-based 3D printing filaments',
                'region': 'North America',
                'investment_range': '100k-500k'
            },
            'description': 'Analyze market opportunities for hemp 3D printing filaments'
        }
    ]
    
    # Insert tasks into database
    inserted_count = 0
    
    for task in tasks:
        try:
            # Check if agent_task_queue table exists
            table_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'agent_task_queue'
                )
            """)
            
            if not table_exists:
                logger.error("agent_task_queue table does not exist. Run migrations first.")
                break
            
            # Insert task
            task_id = await conn.fetchval("""
                INSERT INTO agent_task_queue (
                    agent_type, task_type, priority, parameters, 
                    status, created_at, description
                ) VALUES ($1, $2, $3, $4, 'pending', NOW(), $5)
                RETURNING id
            """, 
            task['agent_type'], 
            task['task_type'], 
            task['priority'],
            json.dumps(task['parameters']),
            task['description']
            )
            
            logger.info(f"Created task {task_id}: {task['description']}")
            inserted_count += 1
            
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            logger.info("Creating task in simplified format...")
            
            # Try alternative approach - add as a product search
            if task['agent_type'] == 'research_agent':
                logger.info(f"Would search for: {task['parameters'].get('query', 'N/A')}")
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"TASK CREATION SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Tasks created: {inserted_count}")
    
    # Check pending tasks
    if table_exists:
        pending_count = await conn.fetchval("""
            SELECT COUNT(*) FROM agent_task_queue 
            WHERE status = 'pending'
        """)
        logger.info(f"Total pending tasks in queue: {pending_count}")
    
    await conn.close()
    logger.info("Database connection closed")
    
    if inserted_count > 0:
        logger.info("\n✅ Tasks created successfully!")
        logger.info("Run 'python run_agent_orchestrator.py' to process these tasks")
    else:
        logger.info("\n⚠️  No tasks were created. The orchestrator tables may not exist.")
        logger.info("Alternative: Run plant-part agents directly:")
        logger.info("  python launch_fiber_agent.py")
        logger.info("  python launch_terpenes_agent.py")
        logger.info("  python launch_roots_agent.py")


if __name__ == "__main__":
    asyncio.run(create_tasks())