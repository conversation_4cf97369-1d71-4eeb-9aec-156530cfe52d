#!/usr/bin/env python3
"""
Test AI-powered product discovery using OpenAI
This demonstrates how agents can discover real products using AI
"""

import os
import logging
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def discover_hemp_products(plant_part: str, count: int = 5):
    """Use AI to discover real hemp products for a given plant part"""
    
    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    prompt = f"""You are a hemp industry expert. Generate {count} real, innovative hemp {plant_part} products that exist in the market today.
    
For each product, provide:
1. Product Name (real product or realistic product name)
2. Description (1-2 sentences)
3. Industry Category
4. Key Benefits (3-5 points)
5. Typical Applications

Focus on innovative, cutting-edge, or unique products. Include specific technical details where relevant.
Format as JSON array with these fields: name, description, industry, benefits, applications"""

    try:
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a hemp industry expert with deep knowledge of hemp products and applications."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=1000
        )
        
        content = response.choices[0].message.content
        logger.info(f"AI Response for {plant_part}:")
        print(content)
        
        return content
        
    except Exception as e:
        logger.error(f"OpenAI API error: {e}")
        return None


def test_multiple_plant_parts():
    """Test AI discovery for multiple plant parts"""
    
    plant_parts = [
        ("Cannabinoids", 3),
        ("Terpenes", 3),
        ("Hemp Fiber", 3),
        ("Hemp Seeds", 3)
    ]
    
    print("\n" + "="*60)
    print("🤖 AI-POWERED HEMP PRODUCT DISCOVERY TEST")
    print("="*60)
    
    for plant_part, count in plant_parts:
        print(f"\n\n🌿 Discovering {plant_part} Products...")
        print("-" * 40)
        
        result = discover_hemp_products(plant_part, count)
        
        if result:
            print("\n✅ Discovery successful!")
        else:
            print("\n❌ Discovery failed - check API key and connection")
        
        print("\n" + "="*60)


def test_innovation_discovery():
    """Test discovering cutting-edge hemp innovations"""
    
    print("\n\n🚀 INNOVATION DISCOVERY TEST")
    print("="*60)
    
    client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    prompt = """List 5 cutting-edge hemp innovations from 2023-2024 that represent breakthrough applications.
Include: graphene from hemp, quantum dots, space materials, bioprinting, or other advanced technologies.
Format as a numbered list with product name and brief description."""
    
    try:
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.8,
            max_tokens=500
        )
        
        print(response.choices[0].message.content)
        
    except Exception as e:
        logger.error(f"Innovation discovery error: {e}")


def main():
    """Run all tests"""
    
    # Check if API key exists
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ No OpenAI API key found in .env file")
        return
    
    # Test basic product discovery
    test_multiple_plant_parts()
    
    # Test innovation discovery
    test_innovation_discovery()
    
    print("\n\n💡 Next Steps:")
    print("1. Parse these AI responses into structured data")
    print("2. Validate products with web searches")
    print("3. Store in database with proper attribution")
    print("4. Set up continuous discovery pipeline")


if __name__ == "__main__":
    main()