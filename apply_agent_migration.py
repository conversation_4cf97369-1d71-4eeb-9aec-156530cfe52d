#!/usr/bin/env python3
"""Apply the agent orchestration tables migration to the database."""
import os
import psycopg2
from urllib.parse import urlparse, unquote

def apply_agent_migration():
    """Apply the agent tables migration."""
    # Get database URL
    database_url = os.environ.get('DATABASE_URL', 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require')
    
    # Parse the URL
    parsed = urlparse(database_url)
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            database=parsed.path[1:],
            user=parsed.username,
            password=unquote(parsed.password),
            sslmode='require'
        )
        
        # Read migration file
        with open('/home/<USER>/projects/HQz-Ai-DB-MCP-3/migrations/create_agent_tables.sql', 'r') as f:
            migration_sql = f.read()
        
        # Execute migration
        with conn.cursor() as cur:
            print("🔧 Applying agent orchestration migration...")
            print("="*60)
            
            # Execute the migration
            cur.execute(migration_sql)
            conn.commit()
            print("✅ Migration applied successfully!")
            
            # Check created tables
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'agent_%'
                ORDER BY table_name
            """)
            tables = [row[0] for row in cur.fetchall()]
            
            print(f"\n📊 Created {len(tables)} agent tables:")
            for table in tables:
                # Get row count
                cur.execute(f"SELECT COUNT(*) FROM {table}")
                count = cur.fetchone()[0]
                print(f"   - {table}: {count} rows")
            
            # Check agent configurations
            cur.execute("SELECT agent_type, is_active FROM agent_configuration ORDER BY agent_type")
            configs = cur.fetchall()
            
            print(f"\n🤖 Agent Configurations:")
            for agent_type, is_active in configs:
                status = "✅ Active" if is_active else "❌ Inactive"
                print(f"   - {agent_type}: {status}")
            
            # Check dependencies
            cur.execute("SELECT service_name, service_type, status FROM agent_dependencies ORDER BY service_type, service_name")
            deps = cur.fetchall()
            
            print(f"\n🔌 Service Dependencies:")
            for service, stype, status in deps:
                emoji = "✅" if status == 'active' else "⚠️"
                print(f"   {emoji} {service} ({stype}): {status}")
        
        conn.close()
        
        print("\n✨ Agent orchestration infrastructure is ready!")
        print("\nNext steps:")
        print("1. Set up environment variables (.env file)")
        print("2. Configure AI provider API keys")
        print("3. Run: python3 run_agent_orchestrator.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error applying migration: {e}")
        return False

if __name__ == "__main__":
    apply_agent_migration()