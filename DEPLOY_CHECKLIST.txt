CLOUD DEPLOYMENT CHECKLIST
==============================

1. Files to copy to cloud server:
   ✅ start_automation.py
   ✅ launch_enhanced_ai_agent.py
   ✅ ai_providers/multi_provider.py
   ✅ config/agent_config.py
   ✅ simple_agent_dashboard.py
   ✅ monitor_automation.py
   ✅ requirements.txt
   ✅ .env.example

2. Database export: database_export_20250702_231607.json

3. Environment variables needed:
   - DATABASE_URL
   - SUPABASE_URL
   - SUPABASE_ANON_KEY
   - DEEPSEEK_API_KEY
   - GEMINI_API_KEY

4. Commands to run on cloud server:
   git clone <your-repo>
   python3 -m venv venv_cloud
   source venv_cloud/bin/activate
   pip install -r requirements.txt
   python import_database.py
   python start_automation.py --continuous
