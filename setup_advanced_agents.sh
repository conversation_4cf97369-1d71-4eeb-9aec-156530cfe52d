#!/bin/bash

echo "🚀 Setting up Advanced Agent System"
echo "=================================="

# Check if virtual environment exists
if [ ! -d "venv_agents" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv_agents
fi

# Activate virtual environment
source venv_agents/bin/activate

echo "📦 Installing essential dependencies..."

# Install essential packages first
pip install --upgrade pip
pip install wheel setuptools

# Core dependencies
pip install python-dotenv aiohttp asyncpg psycopg2-binary beautifulsoup4 requests

# AI/ML essentials (without heavy dependencies)
pip install openai anthropic tiktoken

# NLP basics
pip install rapidfuzz nltk

# Data processing
pip install pandas numpy pydantic

# Monitoring
pip install rich structlog

# For existing langchain
pip install langchain langchain-community

echo ""
echo "✅ Essential dependencies installed!"
echo ""
echo "For full advanced features, run:"
echo "  pip install -r requirements-advanced.txt"
echo ""
echo "Next steps:"
echo "1. Copy env.example to .env and configure"
echo "2. Add your AI provider API keys"
echo "3. Run agents with: python3 run_agent_orchestrator.py"
echo ""
echo "To activate the environment later:"
echo "  source venv_agents/bin/activate"