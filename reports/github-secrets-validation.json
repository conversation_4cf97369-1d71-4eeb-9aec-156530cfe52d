{"validation_timestamp": "2025-07-02T23:50:48.277441", "system_ready": true, "tests_passed": 4, "total_tests": 4, "test_results": {"github_secrets": true, "deepseek_api": true, "gemini_api": true, "openai_api": true}, "provider_priority": [{"name": "DeepSeek", "status": "available", "cost": "$0.0014/1K tokens"}, {"name": "Gemini", "status": "available", "cost": "$0.0075/1K tokens"}, {"name": "OpenAI", "status": "available", "cost": "$0.03/1K tokens"}], "recommendations": []}