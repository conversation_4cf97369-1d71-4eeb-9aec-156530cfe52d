#!/usr/bin/env python3
"""
Generate comprehensive hemp database operation reports
"""

import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

def get_supabase_client():
    """Get Supabase client for database queries"""
    try:
        from supabase import create_client
        
        url = os.environ.get('SUPABASE_URL')
        key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY') or os.environ.get('SUPABASE_ANON_KEY')
        
        if not url or not key:
            return None
        
        return create_client(url, key)
    except:
        return None

def get_database_stats() -> Dict[str, Any]:
    """Get current database statistics"""
    client = get_supabase_client()
    if not client:
        return {'error': 'No database connection'}
    
    try:
        # Get total counts
        products = client.table('uses_products').select('id', count='exact').execute()
        companies = client.table('hemp_companies').select('id', count='exact').execute()
        research = client.table('research_entries').select('id', count='exact').execute()
        
        # Get recent activity (last 24 hours)
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()
        recent_products = client.table('uses_products').select('id').gte('created_at', yesterday).execute()
        recent_companies = client.table('hemp_companies').select('id').gte('created_at', yesterday).execute()
        
        # Get agent activity (last 7 days)
        week_ago = (datetime.now() - timedelta(days=7)).isoformat()
        agent_runs = client.table('hemp_agent_runs').select('*').gte('timestamp', week_ago).execute()
        
        return {
            'total_products': products.count or 0,
            'total_companies': companies.count or 0,
            'total_research': research.count or 0,
            'products_24h': len(recent_products.data) if recent_products.data else 0,
            'companies_24h': len(recent_companies.data) if recent_companies.data else 0,
            'agent_runs_7d': len(agent_runs.data) if agent_runs.data else 0,
            'last_updated': datetime.now().isoformat()
        }
    except Exception as e:
        return {'error': str(e)}

def load_operation_report() -> Optional[Dict]:
    """Load the operation report if it exists"""
    operation = os.environ.get('OPERATION', 'unknown')
    report_file = f'reports/{operation}-report.json'
    
    if os.path.exists(report_file):
        try:
            with open(report_file, 'r') as f:
                return json.load(f)
        except:
            pass
    return None

def generate_markdown_report():
    """Generate a comprehensive markdown report"""
    operation = os.environ.get('OPERATION', 'unknown')
    operation_status = os.environ.get('OPERATION_STATUS', 'unknown')
    
    # Load operation data
    operation_report = load_operation_report()
    db_stats = get_database_stats()
    
    # Create reports directory
    os.makedirs('reports', exist_ok=True)
    
    # Generate markdown report
    report_content = f"""# 🌿 Hemp Database Operations Report

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
**Operation**: {operation.title()}
**Status**: {operation_status.title()}

## 📊 Database Statistics

"""
    
    if 'error' not in db_stats:
        report_content += f"""- **Total Products**: {db_stats['total_products']:,}
- **Total Companies**: {db_stats['total_companies']:,}
- **Total Research Entries**: {db_stats['total_research']:,}
- **Products Added (24h)**: {db_stats['products_24h']}
- **Companies Added (24h)**: {db_stats['companies_24h']}
- **Agent Runs (7d)**: {db_stats['agent_runs_7d']}

"""
    else:
        report_content += f"❌ Database connection error: {db_stats['error']}\n\n"
    
    # Add operation-specific details
    if operation_report:
        report_content += f"""## 🚀 Operation Results

- **Products Discovered**: {operation_report.get('products_discovered', 0)}
- **Products Saved**: {operation_report.get('products_saved', 0)}
- **Test Mode**: {operation_report.get('test_mode', False)}
- **Success**: {operation_report.get('success', False)}

"""
        
        # Add discovered products summary
        if operation_report.get('result') and 'products' in operation_report['result']:
            products = operation_report['result']['products']
            if products:
                report_content += "### 🆕 Newly Discovered Products\n\n"
                for i, product in enumerate(products[:10], 1):  # Show first 10
                    report_content += f"{i}. **{product['name']}** - {product['description'][:100]}...\n"
                
                if len(products) > 10:
                    report_content += f"\n*...and {len(products) - 10} more products*\n"
                report_content += "\n"
    
    # Add health status
    report_content += """## 🏥 System Health

"""
    
    if operation_status == 'success':
        report_content += "✅ **All systems operational**\n"
        report_content += "- Hemp discovery pipeline running smoothly\n"
        report_content += "- Database connections stable\n"
        report_content += "- Automated operations functioning correctly\n"
    else:
        report_content += "⚠️ **Issues detected**\n"
        report_content += "- Some operations may have failed\n"
        report_content += "- Check logs for detailed error information\n"
        report_content += "- Verify all secrets are properly configured\n"
    
    report_content += f"""
## 📈 Growth Metrics

The Hemp Database continues to grow with automated discovery operations:
- **Discovery Rate**: ~{operation_report.get('products_discovered', 0) if operation_report else 0} products per run
- **Success Rate**: {'High' if operation_status == 'success' else 'Needs attention'}
- **Data Quality**: Automated validation and deduplication active

## 🔗 Quick Links

- [View Database](https://ktoqznqmlnxrtvubewyz.supabase.co)
- [GitHub Repository](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3)
- [Action Logs](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/actions)

---
*Report generated by Hemp Database Automation System*
"""
    
    # Save the report
    with open('reports/hemp-operations-report.md', 'w') as f:
        f.write(report_content)
    
    # Also save as JSON for programmatic access
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'operation': operation,
        'status': operation_status,
        'database_stats': db_stats,
        'operation_report': operation_report
    }
    
    with open('reports/hemp-operations-report.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print("📄 Hemp operations report generated successfully!")
    print(f"📊 Database: {db_stats.get('total_products', 0)} products, {db_stats.get('total_companies', 0)} companies")
    if operation_report:
        print(f"🆕 This run: {operation_report.get('products_discovered', 0)} discovered, {operation_report.get('products_saved', 0)} saved")

def main():
    """Generate the hemp operations report"""
    try:
        generate_markdown_report()
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        
        # Create minimal error report
        os.makedirs('reports', exist_ok=True)
        error_report = f"""# Hemp Database Operations Report - Error

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
**Status**: Error

## ❌ Report Generation Failed

Error: {str(e)}

Please check the logs for more details.
"""
        
        with open('reports/hemp-operations-report.md', 'w') as f:
            f.write(error_report)

if __name__ == "__main__":
    main()
