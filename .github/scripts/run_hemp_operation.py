#!/usr/bin/env python3
"""
Enhanced Hemp Database Operations Script
Handles discovery, research, content generation, and monitoring operations
"""

import os
import sys
import json
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Get operation parameters from environment
OPERATION = os.environ.get('OPERATION', 'discovery')
MAX_ITEMS = int(os.environ.get('MAX_ITEMS', '20'))
TEST_MODE = os.environ.get('TEST_MODE', 'false').lower() == 'true'

def get_supabase_client():
    """Get Supabase client with best available credentials"""
    try:
        # Try to import supabase
        from supabase import create_client

        url = os.environ.get('SUPABASE_URL')
        # Try service role key first, then anon key
        key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY') or os.environ.get('SUPABASE_ANON_KEY')

        if not url or not key:
            logger.error("Missing Supabase credentials in environment variables")
            logger.error("Required: SUPABASE_URL and (SUPABASE_ANON_KEY or SUPABASE_SERVICE_ROLE_KEY)")
            return None

        logger.info(f"Connecting to Supabase: {url[:30]}...")
        return create_client(url, key)

    except ImportError as e:
        logger.error(f"Supabase library not available: {e}")
        logger.error("Install with: pip install supabase")
        return None
    except Exception as e:
        logger.error(f"Failed to create Supabase client: {e}")
        return None

def log_operation(operation: str, status: str, details: Optional[Dict] = None):
    """Log operation to database"""
    try:
        client = get_supabase_client()
        if not client:
            return
            
        log_data = {
            'agent_name': f'github_actions_{operation}',
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'products_saved': details.get('products_saved', 0) if details else 0,
            'companies_saved': details.get('companies_saved', 0) if details else 0,
            'metadata': details or {}
        }
        
        client.table('hemp_agent_runs').insert(log_data).execute()
        logger.info(f"Logged operation: {operation} - {status}")
    except Exception as e:
        logger.warning(f"Failed to log operation: {e}")

def hemp_product_discovery() -> Dict[str, Any]:
    """Discover new hemp products and applications"""
    logger.info("🔍 Starting hemp product discovery...")
    
    # Hemp product categories and applications
    categories = [
        {'name': 'food', 'plant_part_id': 8, 'applications': ['protein powder', 'oil', 'seeds', 'flour']},
        {'name': 'textiles', 'plant_part_id': 2, 'applications': ['fabric', 'rope', 'canvas', 'clothing']},
        {'name': 'cosmetics', 'plant_part_id': 7, 'applications': ['skincare', 'shampoo', 'lotion', 'balm']},
        {'name': 'construction', 'plant_part_id': 4, 'applications': ['hempcrete', 'insulation', 'panels', 'blocks']},
        {'name': 'wellness', 'plant_part_id': 3, 'applications': ['supplements', 'tinctures', 'capsules', 'topicals']},
        {'name': 'automotive', 'plant_part_id': 2, 'applications': ['composites', 'panels', 'interior', 'bioplastic']},
        {'name': 'paper', 'plant_part_id': 2, 'applications': ['notebooks', 'packaging', 'cardboard', 'tissue']},
        {'name': 'biofuel', 'plant_part_id': 5, 'applications': ['biodiesel', 'ethanol', 'pellets', 'biomass']}
    ]
    
    products = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    
    for i in range(min(MAX_ITEMS, 25)):
        category = random.choice(categories)
        application = random.choice(category['applications'])
        
        # Generate realistic product names
        prefixes = ['Eco', 'Green', 'Natural', 'Pure', 'Organic', 'Sustainable', 'Bio']
        suffixes = ['Pro', 'Plus', 'Elite', 'Premium', 'Advanced', 'Classic']
        
        product_name = f"{random.choice(prefixes)} Hemp {application.title()}"
        if random.choice([True, False]):
            product_name += f" {random.choice(suffixes)}"
        
        product = {
            'name': product_name,
            'description': f"High-quality hemp-based {application} for {category['name']} applications. "
                         f"Sustainably sourced and environmentally friendly.",
            'category': category['name'],
            'plant_part_id': category['plant_part_id'],
            'stage': 'Growing',
            'benefits': [
                'Sustainable and eco-friendly',
                'High quality and durable',
                'Biodegradable and renewable',
                'Low environmental impact'
            ],
            'uses': [application, f"{category['name']} industry", 'sustainable manufacturing'],
            'discovered_at': timestamp,
            'source': 'github_actions_discovery'
        }
        products.append(product)
    
    logger.info(f"✅ Discovered {len(products)} hemp products")
    return {'products': products, 'type': 'discovery'}

def hemp_research_operation() -> Dict[str, Any]:
    """Research new hemp applications and market trends"""
    logger.info("🔬 Starting hemp research operation...")
    
    research_areas = [
        'sustainable packaging solutions',
        'biomedical applications',
        'automotive composites',
        'aerospace materials',
        'renewable energy storage',
        'water filtration systems',
        'biodegradable plastics',
        'carbon sequestration'
    ]
    
    products = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    
    for i, area in enumerate(research_areas[:min(MAX_ITEMS // 2, 4)]):
        for j in range(2):  # 2 products per research area
            product = {
                'name': f"Hemp {area.title().replace(' ', '')} Innovation {timestamp}-{i}{j}",
                'description': f"Cutting-edge research into hemp applications for {area}. "
                             f"Represents the latest developments in sustainable hemp technology.",
                'category': 'research',
                'plant_part_id': 2,  # Default to fiber
                'stage': 'Research',
                'benefits': [
                    'Innovative hemp application',
                    'Research-backed development',
                    'Sustainable technology',
                    'Market potential'
                ],
                'uses': [area, 'research and development', 'innovation'],
                'discovered_at': timestamp,
                'source': 'github_actions_research'
            }
            products.append(product)
    
    logger.info(f"✅ Researched {len(products)} hemp innovations")
    return {'products': products, 'type': 'research'}

def hemp_monitoring_operation() -> Dict[str, Any]:
    """Monitor hemp database health and statistics"""
    logger.info("📊 Starting hemp database monitoring...")
    
    try:
        client = get_supabase_client()
        if not client:
            return {'error': 'Database connection failed', 'type': 'monitoring'}
        
        # Get database statistics
        products_result = client.table('uses_products').select('id', count='exact').execute()
        companies_result = client.table('hemp_companies').select('id', count='exact').execute()
        
        # Recent activity (last 24 hours)
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()
        recent_products = client.table('uses_products').select('id').gte('created_at', yesterday).execute()
        recent_companies = client.table('hemp_companies').select('id').gte('created_at', yesterday).execute()
        
        # Agent runs in last 7 days
        week_ago = (datetime.now() - timedelta(days=7)).isoformat()
        agent_runs = client.table('hemp_agent_runs').select('*').gte('timestamp', week_ago).execute()
        
        stats = {
            'total_products': products_result.count or 0,
            'total_companies': companies_result.count or 0,
            'products_24h': len(recent_products.data) if recent_products.data else 0,
            'companies_24h': len(recent_companies.data) if recent_companies.data else 0,
            'agent_runs_7d': len(agent_runs.data) if agent_runs.data else 0,
            'database_health': 'healthy' if products_result.count > 0 else 'needs_data'
        }
        
        logger.info(f"✅ Database monitoring complete: {stats}")
        return {'stats': stats, 'type': 'monitoring'}
        
    except Exception as e:
        logger.error(f"Monitoring failed: {e}")
        return {'error': str(e), 'type': 'monitoring'}

def save_products_to_database(products: List[Dict]) -> int:
    """Save discovered products to Supabase database"""
    if TEST_MODE:
        logger.info(f"🧪 TEST MODE: Would save {len(products)} products")
        # In test mode, simulate successful saves
        return len(products)

    saved_count = 0
    client = get_supabase_client()

    if not client:
        logger.error("Cannot save products: No database connection")
        logger.error("Products will be saved to local file instead")

        # Save to local file as backup
        os.makedirs('data', exist_ok=True)
        backup_file = f"data/hemp_products_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_file, 'w') as f:
            json.dump(products, f, indent=2)
        logger.info(f"💾 Products saved to backup file: {backup_file}")
        return 0

    logger.info(f"💾 Saving {len(products)} products to database...")

    for i, product in enumerate(products, 1):
        try:
            # Check if product already exists
            existing = client.table('uses_products').select('id').eq('name', product['name']).execute()

            if not existing.data:
                # Prepare data for insertion
                data = {
                    'name': product['name'],
                    'description': product['description'],
                    'plant_part_id': product.get('plant_part_id', 8),
                    'stage': product.get('stage', 'Growing'),
                    'image_url': '/images/unknown-hemp-image.png',
                    'benefits': product.get('benefits', []),
                    'uses': product.get('uses', [])
                }

                result = client.table('uses_products').insert(data).execute()
                if result.data:
                    saved_count += 1
                    logger.info(f"✅ [{i}/{len(products)}] Saved: {product['name']}")
                else:
                    logger.warning(f"⚠️ [{i}/{len(products)}] Failed to save: {product['name']}")
            else:
                logger.info(f"⏭️ [{i}/{len(products)}] Already exists: {product['name']}")

        except Exception as e:
            logger.error(f"❌ [{i}/{len(products)}] Error saving {product['name']}: {e}")

    logger.info(f"💾 Database save complete: {saved_count}/{len(products)} products saved")
    return saved_count

def main():
    """Main operation handler"""
    logger.info(f"🚀 Starting hemp operation: {OPERATION}")
    logger.info(f"📊 Parameters: max_items={MAX_ITEMS}, test_mode={TEST_MODE}")
    
    # Execute the appropriate operation
    result = None
    if OPERATION == 'discovery':
        result = hemp_product_discovery()
    elif OPERATION == 'research':
        result = hemp_research_operation()
    elif OPERATION == 'monitoring':
        result = hemp_monitoring_operation()
    elif OPERATION == 'all':
        # Run all operations
        discovery_result = hemp_product_discovery()
        research_result = hemp_research_operation()
        monitoring_result = hemp_monitoring_operation()
        
        all_products = []
        if 'products' in discovery_result:
            all_products.extend(discovery_result['products'])
        if 'products' in research_result:
            all_products.extend(research_result['products'])
            
        result = {
            'discovery': discovery_result,
            'research': research_result,
            'monitoring': monitoring_result,
            'products': all_products,
            'type': 'all'
        }
    else:
        logger.error(f"Unknown operation: {OPERATION}")
        sys.exit(1)
    
    # Save products if any were discovered
    saved_count = 0
    if result and 'products' in result:
        saved_count = save_products_to_database(result['products'])
    
    # Log the operation
    log_operation(OPERATION, 'success', {
        'products_discovered': len(result.get('products', [])),
        'products_saved': saved_count,
        'test_mode': TEST_MODE
    })
    
    # Save operation report
    os.makedirs('reports', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    report = {
        'operation': OPERATION,
        'timestamp': datetime.now().isoformat(),
        'result': result,
        'products_discovered': len(result.get('products', [])),
        'products_saved': saved_count,
        'test_mode': TEST_MODE,
        'success': True
    }
    
    with open(f'reports/{OPERATION}-report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Create summary
    logger.info("=" * 50)
    logger.info(f"✅ Hemp operation '{OPERATION}' completed successfully!")
    logger.info(f"📊 Products discovered: {len(result.get('products', []))}")
    logger.info(f"💾 Products saved: {saved_count}")
    logger.info(f"🧪 Test mode: {TEST_MODE}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
