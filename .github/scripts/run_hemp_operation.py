#!/usr/bin/env python3
"""
Enhanced Hemp Database Operations Script with Multi-Provider AI Support
Handles discovery, research, content generation, and monitoring operations
Features cost-optimized AI provider routing (DeepSeek → Gemini → OpenAI)
"""

import os
import sys
import json
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import AI provider manager
try:
    from ai_provider_manager import ai_manager
    AI_AVAILABLE = True
    logger.info("🤖 AI Provider Manager loaded successfully")
except ImportError as e:
    AI_AVAILABLE = False
    logger.warning(f"⚠️ AI Provider Manager not available: {e}")
    logger.info("🔧 Will use fallback content generation")

# Get operation parameters from environment
OPERATION = os.environ.get('OPERATION', 'discovery')
MAX_ITEMS = int(os.environ.get('MAX_ITEMS', '20'))
TEST_MODE = os.environ.get('TEST_MODE', 'false').lower() == 'true'

def get_supabase_client():
    """Get Supabase client with best available credentials"""
    try:
        # Try to import supabase
        from supabase import create_client

        url = os.environ.get('SUPABASE_URL')
        # Try service role key first, then anon key
        key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY') or os.environ.get('SUPABASE_ANON_KEY')

        if not url or not key:
            logger.error("Missing Supabase credentials in environment variables")
            logger.error("Required: SUPABASE_URL and (SUPABASE_ANON_KEY or SUPABASE_SERVICE_ROLE_KEY)")
            return None

        logger.info(f"Connecting to Supabase: {url[:30]}...")
        return create_client(url, key)

    except ImportError as e:
        logger.error(f"Supabase library not available: {e}")
        logger.error("Install with: pip install supabase")
        return None
    except Exception as e:
        logger.error(f"Failed to create Supabase client: {e}")
        return None

def log_operation(operation: str, status: str, details: Optional[Dict] = None):
    """Log operation to database"""
    try:
        client = get_supabase_client()
        if not client:
            return
            
        log_data = {
            'agent_name': f'github_actions_{operation}',
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'products_saved': details.get('products_saved', 0) if details else 0,
            'companies_saved': details.get('companies_saved', 0) if details else 0,
            'metadata': details or {}
        }
        
        client.table('hemp_agent_runs').insert(log_data).execute()
        logger.info(f"Logged operation: {operation} - {status}")
    except Exception as e:
        logger.warning(f"Failed to log operation: {e}")

def generate_ai_enhanced_description(category: str, application: str) -> str:
    """Generate AI-enhanced product description using cost-optimized providers"""
    if not AI_AVAILABLE:
        return f"High-quality hemp-based {application} for {category} applications. Sustainably sourced and environmentally friendly."

    prompt = f"""Generate a detailed, realistic product description for a hemp {application} used in the {category} industry.
    Include specific benefits, applications, and technical details. Keep it under 150 words and make it sound professional and marketable.
    Focus on sustainability, quality, and practical applications."""

    try:
        description, cost = ai_manager.generate_hemp_content(prompt, max_tokens=200)
        logger.info(f"💡 AI-generated description (${cost:.4f})")
        return description.strip()
    except Exception as e:
        logger.warning(f"AI description generation failed: {e}")
        return f"Premium hemp-based {application} designed for {category} applications. Features superior quality, sustainability, and performance characteristics that make it ideal for professional use."

def hemp_product_discovery() -> Dict[str, Any]:
    """Discover new hemp products and applications with AI enhancement"""
    logger.info("🔍 Starting AI-enhanced hemp product discovery...")

    # Hemp product categories and applications
    categories = [
        {'name': 'food', 'plant_part_id': 8, 'applications': ['protein powder', 'oil', 'seeds', 'flour', 'beverages', 'snacks']},
        {'name': 'textiles', 'plant_part_id': 2, 'applications': ['fabric', 'rope', 'canvas', 'clothing', 'upholstery', 'carpets']},
        {'name': 'cosmetics', 'plant_part_id': 7, 'applications': ['skincare', 'shampoo', 'lotion', 'balm', 'soap', 'moisturizer']},
        {'name': 'construction', 'plant_part_id': 4, 'applications': ['hempcrete', 'insulation', 'panels', 'blocks', 'flooring', 'roofing']},
        {'name': 'wellness', 'plant_part_id': 3, 'applications': ['supplements', 'tinctures', 'capsules', 'topicals', 'aromatherapy', 'massage oils']},
        {'name': 'automotive', 'plant_part_id': 2, 'applications': ['composites', 'panels', 'interior', 'bioplastic', 'seat covers', 'dashboards']},
        {'name': 'paper', 'plant_part_id': 2, 'applications': ['notebooks', 'packaging', 'cardboard', 'tissue', 'filters', 'labels']},
        {'name': 'biofuel', 'plant_part_id': 5, 'applications': ['biodiesel', 'ethanol', 'pellets', 'biomass', 'charcoal', 'gas']}
    ]

    products = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    total_ai_cost = 0.0

    # Use AI for a portion of products to balance cost and quality
    ai_enhanced_count = min(MAX_ITEMS // 3, 8)  # Limit AI usage for cost control

    for i in range(min(MAX_ITEMS, 25)):
        category = random.choice(categories)
        application = random.choice(category['applications'])

        # Generate realistic product names
        prefixes = ['Eco', 'Green', 'Natural', 'Pure', 'Organic', 'Sustainable', 'Bio', 'Hemp', 'Elite']
        suffixes = ['Pro', 'Plus', 'Elite', 'Premium', 'Advanced', 'Classic', 'Max', 'Ultra']

        product_name = f"{random.choice(prefixes)} Hemp {application.title()}"
        if random.choice([True, False]):
            product_name += f" {random.choice(suffixes)}"

        # Use AI for enhanced descriptions on some products
        if i < ai_enhanced_count and AI_AVAILABLE:
            description = generate_ai_enhanced_description(category['name'], application)
        else:
            description = f"High-quality hemp-based {application} for {category['name']} applications. Sustainably sourced and environmentally friendly with superior performance characteristics."

        product = {
            'name': product_name,
            'description': description,
            'category': category['name'],
            'plant_part_id': category['plant_part_id'],
            'stage': 'Growing',
            'benefits': [
                'Sustainable and eco-friendly',
                'High quality and durable',
                'Biodegradable and renewable',
                'Low environmental impact',
                'Superior performance',
                'Cost-effective solution'
            ],
            'uses': [application, f"{category['name']} industry", 'sustainable manufacturing', 'green technology'],
            'discovered_at': timestamp,
            'source': 'github_actions_ai_discovery'
        }
        products.append(product)

    # Get AI usage summary
    ai_summary = {}
    if AI_AVAILABLE:
        ai_summary = ai_manager.get_usage_summary()
        total_ai_cost = ai_summary.get('total_cost', 0.0)

    logger.info(f"✅ Discovered {len(products)} hemp products (AI cost: ${total_ai_cost:.4f})")
    return {
        'products': products,
        'type': 'discovery',
        'ai_cost': total_ai_cost,
        'ai_summary': ai_summary
    }

def hemp_research_operation() -> Dict[str, Any]:
    """Research new hemp applications and market trends with AI enhancement"""
    logger.info("🔬 Starting AI-enhanced hemp research operation...")

    research_areas = [
        'sustainable packaging solutions',
        'biomedical applications',
        'automotive composites',
        'aerospace materials',
        'renewable energy storage',
        'water filtration systems',
        'biodegradable plastics',
        'carbon sequestration',
        'smart textiles',
        'nanotechnology applications'
    ]

    products = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    total_ai_cost = 0.0

    # Limit research items for cost control
    research_count = min(MAX_ITEMS // 2, 6)

    for i, area in enumerate(research_areas[:research_count]):
        # Generate AI-enhanced research description
        if AI_AVAILABLE and i < 3:  # Use AI for first 3 research areas
            prompt = f"""Generate a detailed description of cutting-edge hemp research in {area}.
            Include specific technical innovations, potential applications, and market opportunities.
            Focus on realistic but advanced hemp technology developments. Keep under 200 words."""

            try:
                ai_description, cost = ai_manager.generate_hemp_content(prompt, max_tokens=250)
                total_ai_cost += cost
                description = ai_description.strip()
                logger.info(f"🧪 AI research description generated (${cost:.4f})")
            except Exception as e:
                logger.warning(f"AI research generation failed: {e}")
                description = f"Cutting-edge research into hemp applications for {area}. Represents the latest developments in sustainable hemp technology with significant market potential."
        else:
            description = f"Advanced hemp research focusing on {area}. Innovative applications of hemp materials in emerging technologies with sustainable and performance benefits."

        product = {
            'name': f"Hemp {area.title().replace(' ', '')} Research {timestamp}-{i}",
            'description': description,
            'category': 'research',
            'plant_part_id': 2,  # Default to fiber
            'stage': 'Research',
            'benefits': [
                'Innovative hemp application',
                'Research-backed development',
                'Sustainable technology',
                'Market potential',
                'Advanced materials science',
                'Environmental benefits'
            ],
            'uses': [area, 'research and development', 'innovation', 'advanced materials'],
            'discovered_at': timestamp,
            'source': 'github_actions_ai_research'
        }
        products.append(product)

    # Get AI usage summary
    ai_summary = {}
    if AI_AVAILABLE:
        ai_summary = ai_manager.get_usage_summary()

    logger.info(f"✅ Researched {len(products)} hemp innovations (AI cost: ${total_ai_cost:.4f})")
    return {
        'products': products,
        'type': 'research',
        'ai_cost': total_ai_cost,
        'ai_summary': ai_summary
    }

def hemp_monitoring_operation() -> Dict[str, Any]:
    """Monitor hemp database health and statistics"""
    logger.info("📊 Starting hemp database monitoring...")
    
    try:
        client = get_supabase_client()
        if not client:
            return {'error': 'Database connection failed', 'type': 'monitoring'}
        
        # Get database statistics
        products_result = client.table('uses_products').select('id', count='exact').execute()
        companies_result = client.table('hemp_companies').select('id', count='exact').execute()
        
        # Recent activity (last 24 hours)
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()
        recent_products = client.table('uses_products').select('id').gte('created_at', yesterday).execute()
        recent_companies = client.table('hemp_companies').select('id').gte('created_at', yesterday).execute()
        
        # Agent runs in last 7 days
        week_ago = (datetime.now() - timedelta(days=7)).isoformat()
        agent_runs = client.table('hemp_agent_runs').select('*').gte('timestamp', week_ago).execute()
        
        stats = {
            'total_products': products_result.count or 0,
            'total_companies': companies_result.count or 0,
            'products_24h': len(recent_products.data) if recent_products.data else 0,
            'companies_24h': len(recent_companies.data) if recent_companies.data else 0,
            'agent_runs_7d': len(agent_runs.data) if agent_runs.data else 0,
            'database_health': 'healthy' if products_result.count > 0 else 'needs_data'
        }
        
        logger.info(f"✅ Database monitoring complete: {stats}")
        return {'stats': stats, 'type': 'monitoring'}
        
    except Exception as e:
        logger.error(f"Monitoring failed: {e}")
        return {'error': str(e), 'type': 'monitoring'}

def save_products_to_database(products: List[Dict]) -> int:
    """Save discovered products to Supabase database"""
    if TEST_MODE:
        logger.info(f"🧪 TEST MODE: Would save {len(products)} products")
        # In test mode, simulate successful saves
        return len(products)

    saved_count = 0
    client = get_supabase_client()

    if not client:
        logger.error("Cannot save products: No database connection")
        logger.error("Products will be saved to local file instead")

        # Save to local file as backup
        os.makedirs('data', exist_ok=True)
        backup_file = f"data/hemp_products_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_file, 'w') as f:
            json.dump(products, f, indent=2)
        logger.info(f"💾 Products saved to backup file: {backup_file}")
        return 0

    logger.info(f"💾 Saving {len(products)} products to database...")

    for i, product in enumerate(products, 1):
        try:
            # Check if product already exists
            existing = client.table('uses_products').select('id').eq('name', product['name']).execute()

            if not existing.data:
                # Prepare data for insertion
                data = {
                    'name': product['name'],
                    'description': product['description'],
                    'plant_part_id': product.get('plant_part_id', 8),
                    'stage': product.get('stage', 'Growing'),
                    'image_url': '/images/unknown-hemp-image.png',
                    'benefits': product.get('benefits', []),
                    'uses': product.get('uses', [])
                }

                result = client.table('uses_products').insert(data).execute()
                if result.data:
                    saved_count += 1
                    logger.info(f"✅ [{i}/{len(products)}] Saved: {product['name']}")
                else:
                    logger.warning(f"⚠️ [{i}/{len(products)}] Failed to save: {product['name']}")
            else:
                logger.info(f"⏭️ [{i}/{len(products)}] Already exists: {product['name']}")

        except Exception as e:
            logger.error(f"❌ [{i}/{len(products)}] Error saving {product['name']}: {e}")

    logger.info(f"💾 Database save complete: {saved_count}/{len(products)} products saved")
    return saved_count

def main():
    """Main operation handler"""
    logger.info(f"🚀 Starting hemp operation: {OPERATION}")
    logger.info(f"📊 Parameters: max_items={MAX_ITEMS}, test_mode={TEST_MODE}")
    
    # Execute the appropriate operation
    result = None
    if OPERATION == 'discovery':
        result = hemp_product_discovery()
    elif OPERATION == 'research':
        result = hemp_research_operation()
    elif OPERATION == 'monitoring':
        result = hemp_monitoring_operation()
    elif OPERATION == 'all':
        # Run all operations
        discovery_result = hemp_product_discovery()
        research_result = hemp_research_operation()
        monitoring_result = hemp_monitoring_operation()
        
        all_products = []
        if 'products' in discovery_result:
            all_products.extend(discovery_result['products'])
        if 'products' in research_result:
            all_products.extend(research_result['products'])
            
        result = {
            'discovery': discovery_result,
            'research': research_result,
            'monitoring': monitoring_result,
            'products': all_products,
            'type': 'all'
        }
    else:
        logger.error(f"Unknown operation: {OPERATION}")
        sys.exit(1)
    
    # Save products if any were discovered
    saved_count = 0
    if result and 'products' in result:
        saved_count = save_products_to_database(result['products'])
    
    # Calculate total AI costs
    total_ai_cost = 0.0
    ai_summary = {}
    if AI_AVAILABLE:
        ai_summary = ai_manager.get_usage_summary()
        total_ai_cost = ai_summary.get('total_cost', 0.0)

        # Save AI usage report
        ai_manager.save_usage_report('reports/ai-usage-report.json')

    # Log the operation
    log_operation(OPERATION, 'success', {
        'products_discovered': len(result.get('products', [])),
        'products_saved': saved_count,
        'test_mode': TEST_MODE,
        'ai_cost': total_ai_cost,
        'ai_providers_used': list(ai_summary.get('providers', {}).keys()) if ai_summary else []
    })

    # Save operation report
    os.makedirs('reports', exist_ok=True)
    os.makedirs('logs', exist_ok=True)

    report = {
        'operation': OPERATION,
        'timestamp': datetime.now().isoformat(),
        'result': result,
        'products_discovered': len(result.get('products', [])),
        'products_saved': saved_count,
        'test_mode': TEST_MODE,
        'ai_cost': total_ai_cost,
        'ai_summary': ai_summary,
        'success': True
    }

    with open(f'reports/{OPERATION}-report.json', 'w') as f:
        json.dump(report, f, indent=2)

    # Create summary
    logger.info("=" * 60)
    logger.info(f"✅ Hemp operation '{OPERATION}' completed successfully!")
    logger.info(f"📊 Products discovered: {len(result.get('products', []))}")
    logger.info(f"💾 Products saved: {saved_count}")
    logger.info(f"🧪 Test mode: {TEST_MODE}")
    logger.info(f"💰 AI cost: ${total_ai_cost:.4f}")
    if ai_summary and ai_summary.get('providers'):
        logger.info(f"🤖 AI providers used: {', '.join(ai_summary['providers'].keys())}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
