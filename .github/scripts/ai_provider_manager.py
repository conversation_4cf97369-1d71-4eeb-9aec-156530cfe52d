#!/usr/bin/env python3
"""
Cost-Optimized Multi-Provider AI Manager for Hemp Database Automation
Supports DeepSeek, Gemini, and OpenAI with intelligent cost routing
"""

import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class ProviderConfig:
    """Configuration for an AI provider"""
    name: str
    api_key_env: str
    cost_per_1k_tokens: float
    priority: int  # Lower number = higher priority
    max_tokens: int = 4000
    temperature: float = 0.7
    available: bool = False

@dataclass
class UsageStats:
    """Track usage statistics for cost monitoring"""
    provider: str
    tokens_used: int
    requests_made: int
    cost_estimate: float
    timestamp: str

class AIProviderManager:
    """Manages multiple AI providers with cost optimization"""
    
    def __init__(self):
        self.providers = {
            'deepseek': ProviderConfig(
                name='DeepSeek',
                api_key_env='DEEPSEEK_API_KEY',
                cost_per_1k_tokens=0.0014,  # Very low cost
                priority=1,
                max_tokens=4000,
                temperature=0.7
            ),
            'gemini': ProviderConfig(
                name='Gemini',
                api_key_env='GEMINI_API_KEY', 
                cost_per_1k_tokens=0.0075,  # Medium cost
                priority=2,
                max_tokens=4000,
                temperature=0.7
            ),
            'openai': ProviderConfig(
                name='OpenAI',
                api_key_env='OPENAI_API_KEY',
                cost_per_1k_tokens=0.03,    # Highest cost - fallback only
                priority=3,
                max_tokens=4000,
                temperature=0.7
            )
        }
        
        self.usage_stats = []
        self.current_session_cost = 0.0
        self._check_provider_availability()
    
    def _check_provider_availability(self):
        """Check which providers are available based on API keys"""
        available_providers = []
        
        for key, provider in self.providers.items():
            api_key = os.environ.get(provider.api_key_env)
            if api_key and api_key.strip():
                provider.available = True
                available_providers.append(provider.name)
                logger.info(f"✅ {provider.name} available (Priority {provider.priority}, ${provider.cost_per_1k_tokens:.4f}/1K tokens)")
            else:
                provider.available = False
                logger.info(f"❌ {provider.name} not available (missing {provider.api_key_env})")
        
        if not available_providers:
            logger.warning("⚠️ No AI providers available - operations will use fallback methods")
        else:
            logger.info(f"🤖 Available providers: {', '.join(available_providers)}")
    
    def get_best_provider(self) -> Optional[ProviderConfig]:
        """Get the best available provider based on cost priority"""
        available = [p for p in self.providers.values() if p.available]
        if not available:
            return None
        
        # Sort by priority (lower number = higher priority)
        best = min(available, key=lambda p: p.priority)
        logger.info(f"🎯 Selected provider: {best.name} (${best.cost_per_1k_tokens:.4f}/1K tokens)")
        return best
    
    def generate_hemp_content(self, prompt: str, max_tokens: int = 500) -> Tuple[str, float]:
        """Generate hemp-related content using the best available provider"""
        provider = self.get_best_provider()
        
        if not provider:
            logger.warning("No AI providers available, using fallback content generation")
            return self._fallback_content_generation(prompt), 0.0
        
        try:
            if provider.name == 'DeepSeek':
                return self._call_deepseek(prompt, max_tokens, provider)
            elif provider.name == 'Gemini':
                return self._call_gemini(prompt, max_tokens, provider)
            elif provider.name == 'OpenAI':
                return self._call_openai(prompt, max_tokens, provider)
            else:
                logger.error(f"Unknown provider: {provider.name}")
                return self._fallback_content_generation(prompt), 0.0
                
        except Exception as e:
            logger.error(f"Error with {provider.name}: {e}")
            # Try next available provider
            return self._try_fallback_provider(prompt, max_tokens, provider)
    
    def _call_deepseek(self, prompt: str, max_tokens: int, provider: ProviderConfig) -> Tuple[str, float]:
        """Call DeepSeek API"""
        try:
            import requests
            
            api_key = os.environ.get(provider.api_key_env)
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': 'deepseek-chat',
                'messages': [
                    {'role': 'system', 'content': 'You are an expert in industrial hemp products and applications. Generate realistic, detailed information about hemp products.'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': max_tokens,
                'temperature': provider.temperature
            }
            
            response = requests.post(
                'https://api.deepseek.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                tokens_used = result.get('usage', {}).get('total_tokens', max_tokens)
                cost = (tokens_used / 1000) * provider.cost_per_1k_tokens
                
                self._log_usage(provider.name, tokens_used, cost)
                return content, cost
            else:
                logger.error(f"DeepSeek API error: {response.status_code} - {response.text}")
                raise Exception(f"API error: {response.status_code}")
                
        except ImportError:
            logger.error("requests library not available for DeepSeek API")
            raise Exception("Missing requests library")
        except Exception as e:
            logger.error(f"DeepSeek API call failed: {e}")
            raise
    
    def _call_gemini(self, prompt: str, max_tokens: int, provider: ProviderConfig) -> Tuple[str, float]:
        """Call Google Gemini API"""
        try:
            import requests
            
            api_key = os.environ.get(provider.api_key_env)
            url = f'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}'
            
            data = {
                'contents': [{
                    'parts': [{
                        'text': f"You are an expert in industrial hemp products and applications. Generate realistic, detailed information about hemp products.\n\n{prompt}"
                    }]
                }],
                'generationConfig': {
                    'maxOutputTokens': max_tokens,
                    'temperature': provider.temperature
                }
            }
            
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                content = result['candidates'][0]['content']['parts'][0]['text']
                # Gemini doesn't always return token usage, estimate it
                tokens_used = len(content.split()) * 1.3  # Rough estimate
                cost = (tokens_used / 1000) * provider.cost_per_1k_tokens
                
                self._log_usage(provider.name, int(tokens_used), cost)
                return content, cost
            else:
                logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                raise Exception(f"API error: {response.status_code}")
                
        except ImportError:
            logger.error("requests library not available for Gemini API")
            raise Exception("Missing requests library")
        except Exception as e:
            logger.error(f"Gemini API call failed: {e}")
            raise
    
    def _call_openai(self, prompt: str, max_tokens: int, provider: ProviderConfig) -> Tuple[str, float]:
        """Call OpenAI API"""
        try:
            import requests
            
            api_key = os.environ.get(provider.api_key_env)
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': 'gpt-3.5-turbo',
                'messages': [
                    {'role': 'system', 'content': 'You are an expert in industrial hemp products and applications. Generate realistic, detailed information about hemp products.'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': max_tokens,
                'temperature': provider.temperature
            }
            
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                tokens_used = result.get('usage', {}).get('total_tokens', max_tokens)
                cost = (tokens_used / 1000) * provider.cost_per_1k_tokens
                
                self._log_usage(provider.name, tokens_used, cost)
                return content, cost
            else:
                logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                raise Exception(f"API error: {response.status_code}")
                
        except ImportError:
            logger.error("requests library not available for OpenAI API")
            raise Exception("Missing requests library")
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise
    
    def _try_fallback_provider(self, prompt: str, max_tokens: int, failed_provider: ProviderConfig) -> Tuple[str, float]:
        """Try the next available provider if the primary fails"""
        available = [p for p in self.providers.values() if p.available and p.name != failed_provider.name]
        
        if not available:
            logger.warning("No fallback providers available")
            return self._fallback_content_generation(prompt), 0.0
        
        # Get next best provider
        next_provider = min(available, key=lambda p: p.priority)
        logger.info(f"🔄 Falling back to {next_provider.name}")
        
        try:
            if next_provider.name == 'DeepSeek':
                return self._call_deepseek(prompt, max_tokens, next_provider)
            elif next_provider.name == 'Gemini':
                return self._call_gemini(prompt, max_tokens, next_provider)
            elif next_provider.name == 'OpenAI':
                return self._call_openai(prompt, max_tokens, next_provider)
        except Exception as e:
            logger.error(f"Fallback provider {next_provider.name} also failed: {e}")
            return self._fallback_content_generation(prompt), 0.0
    
    def _fallback_content_generation(self, prompt: str) -> str:
        """Generate content without AI when no providers are available"""
        logger.info("🔧 Using fallback content generation")
        
        # Extract key terms from prompt for hemp product generation
        hemp_categories = ['food', 'textiles', 'cosmetics', 'construction', 'wellness', 'automotive']
        applications = ['oil', 'fiber', 'protein', 'seeds', 'fabric', 'rope', 'skincare', 'supplements']
        
        import random
        category = random.choice(hemp_categories)
        application = random.choice(applications)
        
        return f"High-quality hemp {application} for {category} applications. This sustainable product offers excellent performance while maintaining environmental responsibility. Made from premium hemp materials with superior durability and eco-friendly properties."
    
    def _log_usage(self, provider: str, tokens: int, cost: float):
        """Log usage statistics for cost tracking"""
        usage = UsageStats(
            provider=provider,
            tokens_used=tokens,
            requests_made=1,
            cost_estimate=cost,
            timestamp=datetime.now().isoformat()
        )
        
        self.usage_stats.append(usage)
        self.current_session_cost += cost
        
        logger.info(f"💰 {provider}: {tokens} tokens, ${cost:.4f} (Session total: ${self.current_session_cost:.4f})")
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get usage summary for reporting"""
        if not self.usage_stats:
            return {'total_cost': 0, 'total_tokens': 0, 'total_requests': 0, 'providers': {}}
        
        summary = {
            'total_cost': self.current_session_cost,
            'total_tokens': sum(s.tokens_used for s in self.usage_stats),
            'total_requests': len(self.usage_stats),
            'providers': {}
        }
        
        # Group by provider
        for stat in self.usage_stats:
            if stat.provider not in summary['providers']:
                summary['providers'][stat.provider] = {
                    'tokens': 0,
                    'requests': 0,
                    'cost': 0.0
                }
            
            summary['providers'][stat.provider]['tokens'] += stat.tokens_used
            summary['providers'][stat.provider]['requests'] += 1
            summary['providers'][stat.provider]['cost'] += stat.cost_estimate
        
        return summary
    
    def save_usage_report(self, filepath: str):
        """Save detailed usage report to file"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        report = {
            'session_summary': self.get_usage_summary(),
            'detailed_usage': [asdict(stat) for stat in self.usage_stats],
            'provider_configs': {k: asdict(v) for k, v in self.providers.items()},
            'generated_at': datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"💾 Usage report saved to {filepath}")

# Global instance for easy access
ai_manager = AIProviderManager()
