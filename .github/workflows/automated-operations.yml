name: Automated Hemp Database Operations

on:
  schedule:
    # Hemp database population - every 6 hours
    - cron: '0 */6 * * *'
    # Daily health check and monitoring
    - cron: '0 8 * * *'

  workflow_dispatch:
    inputs:
      operation:
        description: 'Operation to run'
        required: false
        default: 'discovery'
        type: choice
        options:
          - discovery      # Hemp product discovery
          - research       # Research agent
          - content        # Content generation
          - monitoring     # Health check
          - all           # All operations
      max_items:
        description: 'Maximum items to process'
        required: false
        default: '20'
        type: string
      test_mode:
        description: 'Run in test mode (no database saves)'
        required: false
        default: false
        type: boolean

permissions:
  contents: read
  actions: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false  # Don't cancel hemp operations

env:
  PYTHONPATH: ${{ github.workspace }}
  PYTHONUNBUFFERED: 1

jobs:
  # Environment validation and setup
  validate-environment:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    outputs:
      is_valid: ${{ steps.check.outputs.is_valid }}
      operation: ${{ steps.determine.outputs.operation }}
      max_items: ${{ steps.determine.outputs.max_items }}
      test_mode: ${{ steps.determine.outputs.test_mode }}

    steps:
    - name: Validate required secrets
      id: check
      run: |
        MISSING=""
        [[ -z "${{ secrets.SUPABASE_URL }}" ]] && MISSING="$MISSING SUPABASE_URL"
        [[ -z "${{ secrets.SUPABASE_ANON_KEY }}" ]] && MISSING="$MISSING SUPABASE_ANON_KEY"

        if [[ -n "$MISSING" ]]; then
          echo "❌ Missing required secrets:$MISSING"
          echo "📋 Please add these secrets in repository Settings > Secrets and variables > Actions"
          echo "is_valid=false" >> $GITHUB_OUTPUT
          exit 1
        else
          echo "✅ All required secrets are configured"
          echo "is_valid=true" >> $GITHUB_OUTPUT
        fi

    - name: Determine operation parameters
      id: determine
      run: |
        # Set operation based on trigger
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          echo "operation=${{ github.event.inputs.operation || 'discovery' }}" >> $GITHUB_OUTPUT
          echo "max_items=${{ github.event.inputs.max_items || '20' }}" >> $GITHUB_OUTPUT
          echo "test_mode=${{ github.event.inputs.test_mode || 'false' }}" >> $GITHUB_OUTPUT
        else
          # Scheduled operations
          HOUR=$(date +%H)
          if [[ "${{ github.event.schedule }}" == "0 */6 * * *" ]]; then
            echo "operation=discovery" >> $GITHUB_OUTPUT
            echo "max_items=15" >> $GITHUB_OUTPUT
          else
            echo "operation=monitoring" >> $GITHUB_OUTPUT
            echo "max_items=5" >> $GITHUB_OUTPUT
          fi
          echo "test_mode=false" >> $GITHUB_OUTPUT
        fi

  # Hemp Database Operations
  hemp-operations:
    needs: validate-environment
    if: needs.validate-environment.outputs.is_valid == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: |
          requirements.txt
          **/requirements*.txt

    - name: Install dependencies
      run: |
        # Install core dependencies
        pip install --upgrade pip
        if [ -f requirements.txt ]; then
          pip install -r requirements.txt
        else
          # Fallback minimal dependencies
          pip install supabase requests beautifulsoup4 python-dotenv openai pandas
        fi

    - name: Setup environment
      run: |
        # Create necessary directories
        mkdir -p lib agents data logs reports
        touch lib/__init__.py agents/__init__.py

        # Make hemp CLI executable
        chmod +x hemp || echo "Hemp CLI not found, will use Python directly"

    - name: Run hemp database operations
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        OPERATION: ${{ needs.validate-environment.outputs.operation }}
        MAX_ITEMS: ${{ needs.validate-environment.outputs.max_items }}
        TEST_MODE: ${{ needs.validate-environment.outputs.test_mode }}
      run: |
        echo "🚀 Starting hemp database operation: $OPERATION"
        echo "📊 Max items: $MAX_ITEMS | Test mode: $TEST_MODE"

        # Run the operation using our improved script
        python .github/scripts/run_hemp_operation.py

  # Image Generation - DISABLED to prevent over-generation
  # generate-images:
  #   needs: [validate-environment, run-agents]
  #   if: |
  #     always() && 
  #     needs.validate-environment.outputs.is_valid == 'true' && 
  #     (needs.validate-environment.outputs.operation == 'all' || 
  #      needs.validate-environment.outputs.operation == 'images')
  #   runs-on: ubuntu-latest
  #   
  #   steps:
  #   - uses: actions/checkout@v3
  #   
  #   - name: Set up Python
  #     uses: actions/setup-python@v4
  #     with:
  #       python-version: '3.10'
  #       
  #   - name: Install dependencies
  #     run: pip install -r requirements.txt
  #       
  #   - name: Check products without images
  #     id: check-images
  #     run: |
  #       echo "🔍 Image generation disabled - over-generation issue"

  # Generate comprehensive report
  generate-report:
    needs: [validate-environment, hemp-operations]
    if: always() && needs.validate-environment.outputs.is_valid == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'

    - name: Install dependencies
      run: |
        pip install supabase requests python-dotenv

    - name: Generate operation report
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        OPERATION: ${{ needs.validate-environment.outputs.operation }}
        OPERATION_STATUS: ${{ needs.hemp-operations.result }}
      run: |
        python .github/scripts/generate_hemp_report.py

    - name: Upload reports and logs
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: hemp-operations-${{ needs.validate-environment.outputs.operation }}-${{ github.run_number }}
        path: |
          reports/
          logs/
        retention-days: 7
        if-no-files-found: ignore

  # Create workflow summary
  create-summary:
    needs: [validate-environment, hemp-operations, generate-report]
    if: always()
    runs-on: ubuntu-latest

    steps:
    - name: Create comprehensive summary
      run: |
        echo "## 🌿 Hemp Database Operations Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Operation**: ${{ needs.validate-environment.outputs.operation }}" >> $GITHUB_STEP_SUMMARY
        echo "**Max Items**: ${{ needs.validate-environment.outputs.max_items }}" >> $GITHUB_STEP_SUMMARY
        echo "**Test Mode**: ${{ needs.validate-environment.outputs.test_mode }}" >> $GITHUB_STEP_SUMMARY
        echo "**Triggered by**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
        echo "**Time**: $(date +'%Y-%m-%d %H:%M UTC')" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        echo "### Job Results" >> $GITHUB_STEP_SUMMARY
        echo "- Environment Validation: ${{ needs.validate-environment.result == 'success' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "- Hemp Operations: ${{ needs.hemp-operations.result == 'success' && '✅ Success' || needs.hemp-operations.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
        echo "- Report Generation: ${{ needs.generate-report.result == 'success' && '✅ Success' || needs.generate-report.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Add status-specific messages
        if [[ "${{ needs.hemp-operations.result }}" == "success" ]]; then
          echo "🎉 **Hemp database operations completed successfully!**" >> $GITHUB_STEP_SUMMARY
          echo "New hemp products and data have been discovered and added to the database." >> $GITHUB_STEP_SUMMARY
        elif [[ "${{ needs.hemp-operations.result }}" == "failure" ]]; then
          echo "⚠️ **Hemp operations encountered issues.**" >> $GITHUB_STEP_SUMMARY
          echo "Please check the logs for details and ensure all secrets are properly configured." >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "📊 [View Detailed Logs](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
        echo "📁 [Download Reports](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}#artifacts)" >> $GITHUB_STEP_SUMMARY