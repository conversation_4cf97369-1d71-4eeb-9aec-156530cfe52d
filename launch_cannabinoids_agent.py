#!/usr/bin/env python3
"""
Launch the Cannabinoids discovery agent
This agent will search for hemp cannabinoid products and populate the database
"""

import asyncio
import logging
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleSupabaseClient:
    """Minimal Supabase client for agent use"""
    
    def __init__(self, conn):
        self.conn = conn
        
    async def get_plant_part_id(self, name: str) -> int:
        """Get plant part ID by name"""
        result = await self.conn.fetchval(
            "SELECT id FROM plant_parts WHERE name = $1",
            name
        )
        return result
        
    async def get_industry_ids(self) -> dict:
        """Get all industry subcategory IDs"""
        rows = await self.conn.fetch(
            "SELECT id, name FROM industry_sub_categories"
        )
        return {row['name']: row['id'] for row in rows}
        
    async def insert_product(self, product_data: dict) -> int:
        """Insert a new product"""
        query = """
            INSERT INTO uses_products (
                name, description, plant_part_id, industry_sub_category_id,
                benefits_advantages, keywords, source_url, source_type,
                source_agent, confidence_score, verification_status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """
        
        product_id = await self.conn.fetchval(
            query,
            product_data['name'],
            product_data.get('description', ''),
            product_data['plant_part_id'],
            product_data.get('industry_sub_category_id'),
            product_data.get('benefits_advantages', []),
            product_data.get('keywords', []),
            product_data.get('source_url', ''),
            'ai_agent',
            'cannabinoids_agent',
            product_data.get('confidence_score', 0.9),
            'ai_verified'
        )
        
        return product_id
        
    async def check_duplicate(self, name: str, plant_part_id: int) -> bool:
        """Check if product already exists"""
        count = await self.conn.fetchval(
            """
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name) = LOWER($1) AND plant_part_id = $2
            """,
            name, plant_part_id
        )
        return count > 0


async def run_cannabinoids_agent():
    """Run the Cannabinoids discovery agent"""
    
    # Database connection
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    # Connect with statement cache disabled for pgbouncer
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    
    # Create simple Supabase client
    supabase = SimpleSupabaseClient(conn)
    
    # Get Cannabinoids plant part ID
    cannabinoids_id = await supabase.get_plant_part_id('Cannabinoids')
    if not cannabinoids_id:
        logger.error("Cannabinoids plant part not found in database!")
        await conn.close()
        return
        
    logger.info(f"Cannabinoids plant part ID: {cannabinoids_id}")
    
    # Get industry IDs
    industry_ids = await supabase.get_industry_ids()
    logger.info(f"Found {len(industry_ids)} industries")
    
    logger.info("Starting Cannabinoids product discovery...")
    
    # Define cannabinoid products to add
    cannabinoid_products = [
        {
            'name': 'High-Potency CBD Oil 5000mg',
            'description': 'Ultra-concentrated full spectrum CBD oil with 5000mg per bottle. CO2 extracted from organic hemp, third-party tested.',
            'industry': 'Health & Wellness',
            'benefits': ['Contains: CBD, CBG, CBN', 'Potency: 5000mg CBD', 'Full Spectrum', 'CO2 Extraction', 'Third-party tested']
        },
        {
            'name': 'CBG Focus Formula',
            'description': 'Pure CBG isolate formula designed for mental clarity and focus. 99% pure cannabigerol with no THC.',
            'industry': 'Health & Wellness',
            'benefits': ['Contains: CBG', 'Potency: 1000mg CBG', 'CBG Isolate', 'For: Focus, Concentration', 'Third-party tested']
        },
        {
            'name': 'Nano CBD Water Soluble',
            'description': 'Revolutionary nano-enhanced CBD with 10x bioavailability. Water-soluble formula for beverages and fast absorption.',
            'industry': 'Food & Beverage',
            'benefits': ['Contains: CBD', 'Enhanced bioavailability: Nano-enhanced', 'Water-soluble', 'Fast absorption', '10x more effective']
        },
        {
            'name': 'CBN Sleep Capsules',
            'description': 'Specialized CBN capsules for deep sleep. Combined with melatonin and chamomile for synergistic effects.',
            'industry': 'Health & Wellness',
            'benefits': ['Contains: CBN', 'Potency: 10mg per capsule', 'For: Insomnia, Sleep', 'Time-release formula', 'Non-habit forming']
        },
        {
            'name': 'Broad Spectrum CBD Muscle Balm',
            'description': 'Topical CBD balm with menthol and arnica for muscle recovery. 1000mg CBD per jar, THC-free.',
            'industry': 'Sports & Recovery',
            'benefits': ['Contains: CBD, CBC', 'Potency: 1000mg CBD', 'Broad Spectrum', 'For: Recovery, Muscle', 'Fast-acting topical']
        },
        {
            'name': 'CBDA Raw Hemp Extract',
            'description': 'Raw, unheated hemp extract preserving acidic cannabinoids. Rich in CBDA for enhanced anti-inflammatory effects.',
            'industry': 'Health & Wellness',
            'benefits': ['Contains: CBDA, CBGA', 'Raw extract', 'For: Inflammation', 'Whole Plant Extract', 'Cold-processed']
        },
        {
            'name': 'CBD Transdermal Patch',
            'description': 'Extended-release CBD patch providing 24-hour relief. 40mg CBD per patch with enhanced penetration technology.',
            'industry': 'Medical Devices',
            'benefits': ['Contains: CBD', 'Potency: 40mg per patch', 'Transdermal Patch', '24-hour release', 'Discreet delivery']
        },
        {
            'name': 'Pet CBD + CBG Tincture',
            'description': 'Veterinarian-formulated CBD and CBG blend for pets. Bacon flavored for easy administration.',
            'industry': 'Pet Products',
            'benefits': ['Contains: CBD, CBG', 'Pet-safe formula', 'For: Anxiety, Joint Pain', 'Vet approved', 'Third-party tested']
        },
        {
            'name': 'Liposomal Vitamin C + CBD',
            'description': 'Advanced liposomal delivery combining vitamin C with CBD for immune support. Maximum absorption guaranteed.',
            'industry': 'Nutraceuticals',
            'benefits': ['Contains: CBD', 'Enhanced bioavailability: Liposomal delivery', 'Immune support', 'Antioxidant blend', 'Pharmaceutical grade']
        },
        {
            'name': 'CBC Anti-Acne Serum',
            'description': 'Specialized CBC serum for acne-prone skin. Cannabichromene shown to reduce sebum production and inflammation.',
            'industry': 'Cosmetics & Personal Care',
            'benefits': ['Contains: CBC, CBD', 'For: Acne treatment', 'Anti-inflammatory', 'Non-comedogenic', 'Dermatologist tested']
        },
        {
            'name': 'Full Spectrum CBD Suppositories',
            'description': 'Medical-grade CBD suppositories for maximum bioavailability. 50mg CBD each for severe conditions.',
            'industry': 'Pharmaceuticals',
            'benefits': ['Contains: CBD, CBG, CBN', 'Potency: 50mg CBD', 'Full Spectrum', 'Medical grade', '95% bioavailability']
        },
        {
            'name': 'THCV Energy Gummies',
            'description': 'Hemp-derived THCV gummies for energy and appetite control. Zero THC, legal nationwide.',
            'industry': 'Food & Beverage',
            'benefits': ['Contains: THCV', 'For: Energy, Appetite control', 'Sugar-free', 'Vegan formula', 'Lab tested']
        },
        {
            'name': 'CBD Pharmaceutical API',
            'description': 'GMP-certified CBD active pharmaceutical ingredient. 99.9% pure for drug formulation and research.',
            'industry': 'Pharmaceuticals',
            'benefits': ['Contains: CBD', 'Potency: 99.9% pure', 'CBD Isolate', 'GMP certified', 'USP grade']
        },
        {
            'name': 'Minor Cannabinoid Research Kit',
            'description': 'Complete set of rare cannabinoid standards for research. Includes CBL, CBE, and other minor cannabinoids.',
            'industry': 'Pharmaceuticals',
            'benefits': ['Contains: CBL, CBE, CBC', 'Research grade', 'Reference standards', 'COA included', 'For laboratory use']
        },
        {
            'name': 'CBD + Melatonin Sleep Spray',
            'description': 'Fast-acting sublingual spray combining CBD with melatonin. Mint flavored for pleasant use.',
            'industry': 'Health & Wellness',
            'benefits': ['Contains: CBD', 'Sublingual spray', 'For: Sleep', 'Fast-acting', 'Travel-friendly']
        }
    ]
    
    # Insert products
    added_count = 0
    for product_info in cannabinoid_products:
        try:
            # Check if already exists
            if await supabase.check_duplicate(product_info['name'], cannabinoids_id):
                logger.info(f"Product '{product_info['name']}' already exists, skipping")
                continue
                
            # Prepare product data
            product_data = {
                'name': product_info['name'],
                'description': product_info['description'],
                'plant_part_id': cannabinoids_id,
                'industry_sub_category_id': industry_ids.get(product_info['industry']),
                'benefits_advantages': product_info['benefits'],
                'keywords': ['cannabinoids', 'CBD', 'hemp', 'therapeutic', 'wellness'],
                'source_url': 'https://projectcbd.org',
                'confidence_score': 0.9
            }
            
            # Insert product
            product_id = await supabase.insert_product(product_data)
            logger.info(f"Added product: {product_info['name']} (ID: {product_id})")
            added_count += 1
            
            # Small delay to avoid overwhelming the database
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"Error adding product '{product_info['name']}': {e}")
            
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"CANNABINOIDS AGENT SUMMARY")  
    logger.info(f"{'='*60}")
    logger.info(f"Products added: {added_count}")
    
    # Check final count
    total_cannabinoids = await conn.fetchval(
        "SELECT COUNT(*) FROM uses_products WHERE plant_part_id = $1",
        cannabinoids_id
    )
    logger.info(f"Total cannabinoid products in database: {total_cannabinoids}")
    
    await conn.close()
    logger.info("Database connection closed")


if __name__ == "__main__":
    asyncio.run(run_cannabinoids_agent())