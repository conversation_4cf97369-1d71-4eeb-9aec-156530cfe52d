#!/usr/bin/env python3
"""
Automated Orchestrator for Daily Agent Runs
Can be run via cron or systemd for continuous operation
"""

import os
import sys
import asyncio
import logging
import subprocess
from datetime import datetime, time, timedelta
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/orchestrator_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AutomatedOrchestrator:
    """Automated orchestrator for running agents on schedule"""
    
    def __init__(self):
        self.agents = {
            'ai_discovery': {
                'script': 'launch_enhanced_ai_agent.py',
                'schedule': 'daily',
                'time': time(2, 0),  # 2 AM
                'enabled': True,
                'description': 'AI-powered product discovery'
            },
            'deduplication': {
                'script': 'merge_exact_duplicates.py',
                'schedule': 'daily',
                'time': time(3, 0),  # 3 AM
                'enabled': True,
                'description': 'Remove duplicate products'
            },
            'dashboard': {
                'script': 'simple_agent_dashboard.py',
                'schedule': 'daily',
                'time': time(4, 0),  # 4 AM
                'enabled': True,
                'description': 'Generate daily report'
            }
        }
        
        # Track last run times
        self.last_runs = {}
        self.load_state()
    
    def load_state(self):
        """Load last run times from file"""
        state_file = Path("orchestrator_state.json")
        if state_file.exists():
            import json
            try:
                with open(state_file, 'r') as f:
                    data = json.load(f)
                    for agent, timestamp in data.get('last_runs', {}).items():
                        self.last_runs[agent] = datetime.fromisoformat(timestamp)
            except Exception as e:
                logger.error(f"Error loading state: {e}")
    
    def save_state(self):
        """Save last run times to file"""
        import json
        state_file = Path("orchestrator_state.json")
        data = {
            'last_runs': {
                agent: timestamp.isoformat() 
                for agent, timestamp in self.last_runs.items()
            },
            'updated': datetime.now().isoformat()
        }
        with open(state_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def should_run_agent(self, agent_name: str, agent_config: dict) -> bool:
        """Check if agent should run based on schedule"""
        if not agent_config['enabled']:
            return False
            
        now = datetime.now()
        last_run = self.last_runs.get(agent_name)
        
        # First run
        if not last_run:
            return True
            
        # Check schedule
        if agent_config['schedule'] == 'daily':
            # Has it been 24 hours?
            if now - last_run < timedelta(hours=23):
                return False
                
            # Is it the right time?
            scheduled_time = agent_config['time']
            current_time = now.time()
            
            # Allow 1-hour window
            if scheduled_time <= current_time <= time(
                scheduled_time.hour + 1, 
                scheduled_time.minute
            ):
                return True
                
        return False
    
    async def run_agent(self, agent_name: str, agent_config: dict):
        """Run a single agent"""
        logger.info(f"🤖 Running {agent_name}: {agent_config['description']}")
        
        script_path = Path(agent_config['script'])
        if not script_path.exists():
            logger.error(f"Script not found: {script_path}")
            return False
            
        try:
            # Run the agent
            result = subprocess.run(
                [sys.executable, str(script_path), '--auto'],
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {agent_name} completed successfully")
                # Log key output lines
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines[-10:]:
                    if any(keyword in line for keyword in ['added', 'total', 'complete', 'stats']):
                        logger.info(f"  {line.strip()}")
                        
                self.last_runs[agent_name] = datetime.now()
                self.save_state()
                return True
            else:
                logger.error(f"❌ {agent_name} failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏱️ {agent_name} timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Error running {agent_name}: {e}")
            return False
    
    async def run_cycle(self):
        """Run one orchestrator cycle"""
        logger.info("\n" + "="*60)
        logger.info("🔄 ORCHESTRATOR CYCLE STARTING")
        logger.info("="*60)
        
        agents_run = 0
        
        for agent_name, agent_config in self.agents.items():
            if self.should_run_agent(agent_name, agent_config):
                success = await self.run_agent(agent_name, agent_config)
                if success:
                    agents_run += 1
                await asyncio.sleep(5)  # Pause between agents
            else:
                logger.debug(f"Skipping {agent_name} - not scheduled")
        
        logger.info(f"\n✅ Cycle complete. Ran {agents_run} agents")
        return agents_run
    
    async def run_continuous(self):
        """Run orchestrator continuously"""
        logger.info("🚀 AUTOMATED ORCHESTRATOR STARTED")
        logger.info(f"Monitoring {len(self.agents)} agents")
        
        while True:
            try:
                await self.run_cycle()
                
                # Wait 15 minutes before next check
                logger.info("⏳ Waiting 15 minutes before next cycle...")
                await asyncio.sleep(900)
                
            except KeyboardInterrupt:
                logger.info("\n🛑 Orchestrator stopped by user")
                break
            except Exception as e:
                logger.error(f"Orchestrator error: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error


def setup_systemd():
    """Generate systemd service file"""
    service_content = f"""[Unit]
Description=Hemp Database AI Agent Orchestrator
After=network.target

[Service]
Type=simple
User={os.environ.get('USER', 'hempquarterz')}
WorkingDirectory={os.getcwd()}
Environment="PATH={os.path.dirname(sys.executable)}:$PATH"
ExecStart={sys.executable} {os.path.abspath(__file__)} --daemon
Restart=always
RestartSec=60

[Install]
WantedBy=multi-user.target
"""
    
    print("\n📋 SYSTEMD SERVICE CONFIGURATION")
    print("="*60)
    print("\n1. Create service file:")
    print("   sudo nano /etc/systemd/system/hemp-orchestrator.service")
    print("\n2. Paste this content:")
    print("-"*60)
    print(service_content)
    print("-"*60)
    print("\n3. Enable and start service:")
    print("   sudo systemctl daemon-reload")
    print("   sudo systemctl enable hemp-orchestrator")
    print("   sudo systemctl start hemp-orchestrator")
    print("\n4. Check status:")
    print("   sudo systemctl status hemp-orchestrator")
    print("   journalctl -u hemp-orchestrator -f")


def setup_cron():
    """Generate cron configuration"""
    script_path = os.path.abspath(__file__)
    python_path = sys.executable
    
    print("\n📋 CRON CONFIGURATION")
    print("="*60)
    print("\n1. Open crontab:")
    print("   crontab -e")
    print("\n2. Add these lines:")
    print("-"*60)
    print("# Hemp Database AI Orchestrator")
    print("# Run every 15 minutes")
    print(f"*/15 * * * * cd {os.getcwd()} && {python_path} {script_path} --cycle >> logs/cron.log 2>&1")
    print("\n# Or run at specific times")
    print(f"0 2 * * * cd {os.getcwd()} && {python_path} launch_enhanced_ai_agent.py --auto >> logs/ai_discovery.log 2>&1")
    print(f"0 3 * * * cd {os.getcwd()} && {python_path} merge_exact_duplicates.py >> logs/dedup.log 2>&1")
    print(f"0 4 * * * cd {os.getcwd()} && {python_path} simple_agent_dashboard.py >> logs/dashboard.log 2>&1")
    print("-"*60)


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Automated Agent Orchestrator')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    parser.add_argument('--cycle', action='store_true', help='Run single cycle')
    parser.add_argument('--setup-systemd', action='store_true', help='Show systemd setup')
    parser.add_argument('--setup-cron', action='store_true', help='Show cron setup')
    
    args = parser.parse_args()
    
    if args.setup_systemd:
        setup_systemd()
    elif args.setup_cron:
        setup_cron()
    elif args.daemon or args.cycle:
        orchestrator = AutomatedOrchestrator()
        if args.cycle:
            # Run single cycle
            asyncio.run(orchestrator.run_cycle())
        else:
            # Run continuous
            asyncio.run(orchestrator.run_continuous())
    else:
        # Interactive menu
        print("\n" + "="*60)
        print("🤖 AUTOMATED ORCHESTRATOR SETUP")
        print("="*60)
        print("\n1. Run orchestrator now (continuous)")
        print("2. Run single cycle")
        print("3. Setup systemd service (recommended)")
        print("4. Setup cron jobs")
        print("5. View current configuration")
        
        choice = input("\nSelect option (1-5): ")
        
        if choice == '1':
            orchestrator = AutomatedOrchestrator()
            asyncio.run(orchestrator.run_continuous())
        elif choice == '2':
            orchestrator = AutomatedOrchestrator()
            asyncio.run(orchestrator.run_cycle())
        elif choice == '3':
            setup_systemd()
        elif choice == '4':
            setup_cron()
        elif choice == '5':
            orchestrator = AutomatedOrchestrator()
            print("\n📋 Current Configuration:")
            for name, config in orchestrator.agents.items():
                print(f"\n{name}:")
                print(f"  Script: {config['script']}")
                print(f"  Schedule: {config['schedule']} at {config['time']}")
                print(f"  Enabled: {config['enabled']}")
                print(f"  Last run: {orchestrator.last_runs.get(name, 'Never')}")


if __name__ == "__main__":
    main()