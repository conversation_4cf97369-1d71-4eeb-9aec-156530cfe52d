#!/usr/bin/env python3
"""Apply the deduplication migration to the database."""
import os
import psycopg2
from urllib.parse import urlparse, unquote

def apply_migration():
    """Apply the deduplication fields migration."""
    # Get database URL from environment or use default
    database_url = os.environ.get('DATABASE_URL', 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require')
    if not database_url:
        print("Error: DATABASE_URL environment variable not set")
        return False
    
    # Parse the URL
    parsed = urlparse(database_url)
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            database=parsed.path[1:],  # Remove leading slash
            user=parsed.username,
            password=unquote(parsed.password),  # URL decode the password
            sslmode='require'
        )
        
        # Read migration file
        with open('/home/<USER>/projects/HQz-Ai-DB-MCP-3/migrations/add_deduplication_fields.sql', 'r') as f:
            migration_sql = f.read()
        
        # Execute migration
        with conn.cursor() as cur:
            print("Applying deduplication migration...")
            cur.execute(migration_sql)
            conn.commit()
            print("✅ Migration applied successfully!")
            
            # Check if columns were added
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'uses_products' 
                AND column_name IN ('source_url', 'source_type', 'confidence_score', 'canonical_product_id')
            """)
            columns = [row[0] for row in cur.fetchall()]
            print(f"✅ Added columns: {', '.join(columns)}")
            
            # Check new tables
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('uses_products_versions', 'data_sources', 'duplicate_candidates')
            """)
            tables = [row[0] for row in cur.fetchall()]
            print(f"✅ Created tables: {', '.join(tables)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error applying migration: {e}")
        return False

if __name__ == "__main__":
    apply_migration()