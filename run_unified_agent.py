#!/usr/bin/env python3
"""
Unified Agent Runner - Run different agent tasks on demand
No orchestrator tables required - direct execution
"""

import asyncio
import logging
import sys
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def display_menu():
    """Display agent menu options"""
    print("\n" + "="*60)
    print("🤖 UNIFIED AGENT RUNNER")
    print("="*60)
    print("\nAvailable Agent Tasks:")
    print("\n1. 🌿 Plant-Part Discovery")
    print("   - Run specific plant part agents")
    print("   - Discover products for Fiber, Seeds, Flowers, etc.")
    
    print("\n2. 🔬 Innovation Search")
    print("   - Find cutting-edge hemp applications")
    print("   - Advanced materials, biotech, space tech")
    
    print("\n3. 🌍 Sustainability Solutions")
    print("   - Eco-friendly hemp products")
    print("   - Circular economy, carbon capture")
    
    print("\n4. 🏭 Company Discovery")
    print("   - Find hemp companies and manufacturers")
    print("   - Build supplier network")
    
    print("\n5. 📊 Database Analysis")
    print("   - Run analytics and reports")
    print("   - Check gaps and opportunities")
    
    print("\n6. 🔄 Run All Agents")
    print("   - Execute all discovery agents")
    print("   - Maximum database growth")
    
    print("\n7. 📈 View Dashboard")
    print("   - See agent performance")
    print("   - Database statistics")
    
    print("\n0. Exit")
    print("\n" + "="*60)


async def run_plant_part_agent():
    """Run specific plant part agent"""
    print("\nSelect Plant Part:")
    print("1. Cannabinoids (16 products - needs growth)")
    print("2. Terpenes (10 products - needs growth)")
    print("3. Roots (19 products)")
    print("4. Leaves (24 products)")
    print("5. Flowers (33 products)")
    print("6. Hurds/Shivs (51 products)")
    print("7. Seeds (102 products)")
    print("8. Fiber (130 products)")
    
    choice = input("\nEnter choice (1-8): ")
    
    scripts = {
        '1': 'launch_cannabinoids_agent.py',
        '2': 'launch_terpenes_agent.py',
        '3': 'launch_roots_agent.py',
        '4': 'launch_leaves_agent.py',
        '5': 'launch_flowers_agent.py',
        '6': 'launch_hurds_agent.py',
        '7': 'launch_seeds_agent.py',
        '8': 'launch_fiber_agent.py'
    }
    
    script = scripts.get(choice)
    if script:
        print(f"\nRunning {script}...")
        if script == 'launch_cannabinoids_agent.py':
            print("Note: Cannabinoids agent not yet created. Would you like to create it?")
            if input("Create agent? (y/n): ").lower() == 'y':
                print("Creating cannabinoids agent...")
                # Would create the agent here
        else:
            import subprocess
            try:
                subprocess.run([sys.executable, script], check=True)
            except FileNotFoundError:
                print(f"Script {script} not found. Running alternative...")
                subprocess.run([sys.executable, 'launch_all_agents.py'], check=True)
    else:
        print("Invalid choice")


async def run_innovation_search():
    """Run innovation search agent"""
    print("\nRunning Innovation Search Agent...")
    import subprocess
    subprocess.run([sys.executable, 'run_innovation_search.py'], check=True)


async def run_sustainability_agent():
    """Run sustainability solutions agent"""
    print("\nRunning Sustainability Solutions Agent...")
    import subprocess
    subprocess.run([sys.executable, 'run_sustainability_agent.py'], check=True)


async def run_company_discovery():
    """Run company discovery (simplified)"""
    print("\nCompany Discovery Agent")
    print("This would search for hemp companies and add them to the database.")
    print("Currently requires additional setup.")
    print("\nWould you like to run a simplified version? (y/n): ")
    
    if input().lower() == 'y':
        print("Running simplified company search...")
        # Could implement simplified company search here


async def run_database_analysis():
    """Run database analysis"""
    print("\nRunning Database Analysis...")
    import subprocess
    
    # Run status check
    try:
        subprocess.run([sys.executable, 'check_current_status.py'], check=True)
    except:
        print("Status check not available")
    
    # Run dashboard
    subprocess.run([sys.executable, 'simple_agent_dashboard.py'], check=True)


async def run_all_agents():
    """Run all discovery agents"""
    print("\n⚡ Running ALL Discovery Agents")
    print("This will run multiple agents in sequence:")
    print("- Plant-part agents")
    print("- Innovation search")
    print("- Sustainability solutions")
    
    confirm = input("\nContinue? This may take several minutes (y/n): ")
    if confirm.lower() == 'y':
        import subprocess
        
        scripts = [
            'launch_all_agents.py',
            'run_innovation_search.py', 
            'run_sustainability_agent.py'
        ]
        
        for script in scripts:
            print(f"\n{'='*40}")
            print(f"Running {script}")
            print('='*40)
            try:
                subprocess.run([sys.executable, script], check=True)
            except Exception as e:
                print(f"Error running {script}: {e}")
                continue


async def view_dashboard():
    """View agent dashboard"""
    print("\nLoading Agent Dashboard...")
    import subprocess
    subprocess.run([sys.executable, 'simple_agent_dashboard.py'], check=True)


async def main():
    """Main menu loop"""
    print("\n🌿 Welcome to the Hemp Database Agent System!")
    print("Current database: 385+ products across 8 plant parts")
    
    while True:
        display_menu()
        
        try:
            choice = input("\nEnter your choice (0-7): ")
            
            if choice == '0':
                print("\nExiting Agent Runner. Goodbye! 🌿")
                break
            elif choice == '1':
                await run_plant_part_agent()
            elif choice == '2':
                await run_innovation_search()
            elif choice == '3':
                await run_sustainability_agent()
            elif choice == '4':
                await run_company_discovery()
            elif choice == '5':
                await run_database_analysis()
            elif choice == '6':
                await run_all_agents()
            elif choice == '7':
                await view_dashboard()
            else:
                print("Invalid choice. Please try again.")
                
        except KeyboardInterrupt:
            print("\n\nInterrupted by user. Exiting...")
            break
        except Exception as e:
            logger.error(f"Error: {e}")
            print(f"\nError occurred: {e}")
            print("Returning to menu...")
            
        input("\nPress Enter to continue...")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\nAgent Runner stopped by user.")