#!/usr/bin/env python3
"""
Test script for the multi-provider AI system
Validates provider availability, cost optimization, and fallback logic
"""

import os
import sys
import json
from datetime import datetime

def test_environment_setup():
    """Test that AI provider environment variables are configured"""
    print("🔍 Testing AI Provider Environment Setup...")
    
    providers = {
        'DEEPSEEK_API_KEY': {'name': 'DeepSeek', 'priority': 1, 'cost': 0.0014},
        'GEMINI_API_KEY': {'name': 'Gemini', 'priority': 2, 'cost': 0.0075},
        'OPENAI_API_KEY': {'name': 'OpenAI', 'priority': 3, 'cost': 0.03}
    }
    
    available_providers = []
    missing_providers = []
    
    for env_var, info in providers.items():
        api_key = os.environ.get(env_var)
        if api_key and api_key.strip():
            print(f"✅ {info['name']}: Available (Priority {info['priority']}, ${info['cost']:.4f}/1K tokens)")
            available_providers.append(info['name'])
        else:
            print(f"❌ {info['name']}: Missing ({env_var} not set)")
            missing_providers.append(info['name'])
    
    if not available_providers:
        print("⚠️ No AI providers configured - system will use fallback methods")
        return False, missing_providers
    else:
        print(f"🎯 {len(available_providers)} provider(s) available: {', '.join(available_providers)}")
        return True, missing_providers

def test_ai_provider_manager():
    """Test the AI provider manager functionality"""
    print("\n🤖 Testing AI Provider Manager...")
    
    try:
        # Add current directory to path for imports
        sys.path.insert(0, '.github/scripts')
        from ai_provider_manager import AIProviderManager
        
        # Create manager instance
        manager = AIProviderManager()
        print("✅ AI Provider Manager imported successfully")
        
        # Test provider availability check
        best_provider = manager.get_best_provider()
        if best_provider:
            print(f"✅ Best provider selected: {best_provider.name} (${best_provider.cost_per_1k_tokens:.4f}/1K tokens)")
        else:
            print("⚠️ No providers available - will use fallback methods")
        
        # Test content generation (with fallback)
        test_prompt = "Generate a description for hemp protein powder for food applications"
        try:
            content, cost = manager.generate_hemp_content(test_prompt, max_tokens=100)
            print(f"✅ Content generation successful (${cost:.4f})")
            print(f"📝 Sample content: {content[:100]}...")
        except Exception as e:
            print(f"⚠️ Content generation failed: {e}")
        
        # Test usage summary
        summary = manager.get_usage_summary()
        print(f"✅ Usage tracking: ${summary['total_cost']:.4f} total cost")
        
        return True
        
    except ImportError as e:
        print(f"❌ AI Provider Manager import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ AI Provider Manager test failed: {e}")
        return False

def test_hemp_operation_integration():
    """Test integration with hemp operation script"""
    print("\n🌿 Testing Hemp Operation Integration...")
    
    try:
        # Test import
        sys.path.insert(0, '.github/scripts')
        import run_hemp_operation
        print("✅ Hemp operation script imported successfully")
        
        # Test AI availability detection
        if hasattr(run_hemp_operation, 'AI_AVAILABLE'):
            print(f"✅ AI availability detected: {run_hemp_operation.AI_AVAILABLE}")
        
        # Test AI-enhanced description function
        if hasattr(run_hemp_operation, 'generate_ai_enhanced_description'):
            try:
                description = run_hemp_operation.generate_ai_enhanced_description('food', 'protein powder')
                print(f"✅ AI description generation: {len(description)} characters")
                print(f"📝 Sample: {description[:80]}...")
            except Exception as e:
                print(f"⚠️ AI description generation failed: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Hemp operation script import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Hemp operation integration test failed: {e}")
        return False

def test_cost_optimization():
    """Test cost optimization logic"""
    print("\n💰 Testing Cost Optimization...")
    
    try:
        sys.path.insert(0, '.github/scripts')
        from ai_provider_manager import AIProviderManager
        
        manager = AIProviderManager()
        
        # Check provider priority order
        available_providers = [p for p in manager.providers.values() if p.available]
        if available_providers:
            sorted_providers = sorted(available_providers, key=lambda p: p.priority)
            print("✅ Provider priority order:")
            for i, provider in enumerate(sorted_providers, 1):
                print(f"   {i}. {provider.name} - ${provider.cost_per_1k_tokens:.4f}/1K tokens")
            
            # Verify lowest cost provider is selected first
            best = manager.get_best_provider()
            if best and best == sorted_providers[0]:
                print(f"✅ Cost optimization working: {best.name} selected (lowest cost)")
            else:
                print("⚠️ Cost optimization may not be working correctly")
        else:
            print("⚠️ No providers available to test cost optimization")
        
        return True
        
    except Exception as e:
        print(f"❌ Cost optimization test failed: {e}")
        return False

def test_fallback_logic():
    """Test fallback logic when providers fail"""
    print("\n🔄 Testing Fallback Logic...")
    
    try:
        sys.path.insert(0, '.github/scripts')
        from ai_provider_manager import AIProviderManager
        
        manager = AIProviderManager()
        
        # Test fallback content generation
        fallback_content = manager._fallback_content_generation("hemp protein powder for food")
        if fallback_content and len(fallback_content) > 50:
            print("✅ Fallback content generation working")
            print(f"📝 Sample fallback: {fallback_content[:80]}...")
        else:
            print("❌ Fallback content generation failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback logic test failed: {e}")
        return False

def generate_test_report():
    """Generate a comprehensive test report"""
    print("\n📊 Generating Test Report...")
    
    # Run all tests
    env_success, missing_providers = test_environment_setup()
    manager_success = test_ai_provider_manager()
    integration_success = test_hemp_operation_integration()
    cost_success = test_cost_optimization()
    fallback_success = test_fallback_logic()
    
    # Calculate overall success
    total_tests = 5
    passed_tests = sum([env_success, manager_success, integration_success, cost_success, fallback_success])
    
    # Create report
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'overall_success': passed_tests == total_tests,
        'tests_passed': passed_tests,
        'total_tests': total_tests,
        'test_results': {
            'environment_setup': env_success,
            'ai_provider_manager': manager_success,
            'hemp_operation_integration': integration_success,
            'cost_optimization': cost_success,
            'fallback_logic': fallback_success
        },
        'missing_providers': missing_providers,
        'recommendations': []
    }
    
    # Add recommendations
    if not env_success:
        report['recommendations'].append("Add at least one AI provider API key (DEEPSEEK_API_KEY recommended)")
    
    if missing_providers:
        if 'DeepSeek' in missing_providers:
            report['recommendations'].append("Add DEEPSEEK_API_KEY for lowest cost AI operations")
        if 'Gemini' in missing_providers:
            report['recommendations'].append("Add GEMINI_API_KEY for backup AI operations")
    
    if not manager_success:
        report['recommendations'].append("Check AI provider manager dependencies and configuration")
    
    # Save report
    os.makedirs('reports', exist_ok=True)
    with open('reports/ai-provider-test-report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    return report

def main():
    """Run all AI provider tests"""
    print("🚀 Multi-Provider AI System Test Suite")
    print("=" * 50)
    
    # Generate comprehensive test report
    report = generate_test_report()
    
    # Print summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    if report['overall_success']:
        print("🎉 ALL TESTS PASSED!")
        print("Your multi-provider AI system is ready for production use.")
        print("\nNext steps:")
        print("1. Run the hemp automation workflow manually to test")
        print("2. Monitor AI costs in the generated reports")
        print("3. Check that DeepSeek is being used as primary provider")
    else:
        print(f"⚠️ {report['total_tests'] - report['tests_passed']} TEST(S) FAILED")
        print("Please address the issues above before using the AI system.")
        
        if report['recommendations']:
            print("\n🔧 Recommendations:")
            for i, rec in enumerate(report['recommendations'], 1):
                print(f"{i}. {rec}")
    
    print(f"\nResults: {report['tests_passed']}/{report['total_tests']} tests passed")
    print("📄 Detailed report saved to: reports/ai-provider-test-report.json")
    
    return report['overall_success']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
