#!/usr/bin/env python3
"""
Export database for cloud migration
Creates a backup of all products and settings
"""

import os
import json
import asyncio
import asyncpg
from datetime import datetime
from urllib.parse import urlparse, unquote
from dotenv import load_dotenv

load_dotenv()


async def export_database():
    """Export all database content to JSON"""
    
    print("📦 EXPORTING DATABASE FOR CLOUD MIGRATION")
    print("="*50)
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL not found")
        return
        
    parsed = urlparse(database_url)
    
    try:
        conn = await asyncpg.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            database=parsed.path[1:],
            user=parsed.username,
            password=unquote(parsed.password),
            ssl='require',
            statement_cache_size=0
        )
        
        export_data = {
            'export_date': datetime.now().isoformat(),
            'database_stats': {},
            'products': [],
            'companies': [],
            'research': [],
            'plant_parts': [],
            'industries': []
        }
        
        # Export products
        print("\n📦 Exporting products...")
        products = await conn.fetch("""
            SELECT p.*, pp.name as plant_part_name, i.name as industry_name
            FROM uses_products p
            LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
            LEFT JOIN industry_sub_categories i ON p.industry_sub_category_id = i.id
            ORDER BY p.id
        """)
        
        for product in products:
            export_data['products'].append({
                'id': product['id'],
                'name': product['name'],
                'description': product['description'],
                'plant_part': product['plant_part_name'],
                'industry': product['industry_name'],
                'benefits': product['benefits_advantages'],
                'keywords': product['keywords'],
                'source_agent': product['source_agent'],
                'confidence_score': float(product['confidence_score']) if product['confidence_score'] else None,
                'verification_status': product['verification_status'],
                'created_at': product['created_at'].isoformat() if product['created_at'] else None
            })
        
        print(f"✅ Exported {len(products)} products")
        
        # Export companies
        print("\n📦 Exporting companies...")
        companies = await conn.fetch("SELECT * FROM hemp_companies ORDER BY id")
        for company in companies:
            export_data['companies'].append({
                'id': company['id'],
                'name': company['name'],
                'description': company['description'],
                'website': company['website'],
                'logo_url': company['logo_url']
            })
        print(f"✅ Exported {len(companies)} companies")
        
        # Export research
        print("\n📦 Exporting research entries...")
        research = await conn.fetch("SELECT * FROM research_entries ORDER BY id")
        for entry in research:
            export_data['research'].append({
                'id': entry['id'],
                'title': entry['title'],
                'summary': entry.get('summary', ''),
                'url': entry.get('url', ''),
                'image_url': entry.get('image_url', '')
            })
        print(f"✅ Exported {len(research)} research entries")
        
        # Export plant parts
        plant_parts = await conn.fetch("SELECT id, name FROM plant_parts ORDER BY id")
        export_data['plant_parts'] = [{'id': pp['id'], 'name': pp['name']} for pp in plant_parts]
        
        # Export industries
        industries = await conn.fetch("SELECT id, name FROM industry_sub_categories ORDER BY id")
        export_data['industries'] = [{'id': ind['id'], 'name': ind['name']} for ind in industries]
        
        # Database stats
        export_data['database_stats'] = {
            'total_products': len(products),
            'total_companies': len(companies),
            'total_research': len(research),
            'ai_generated_products': len([p for p in products if p['source_agent'] == 'ai_discovery_agent']),
            'verified_products': len([p for p in products if p['verification_status'] == 'ai_verified'])
        }
        
        # Save to file
        filename = f"database_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        print(f"\n✅ Export complete: {filename}")
        print(f"📊 File size: {os.path.getsize(filename) / 1024:.1f} KB")
        
        # Create deployment package
        print("\n📦 Creating deployment package...")
        
        deploy_files = [
            'start_automation.py',
            'launch_enhanced_ai_agent.py',
            'ai_providers/multi_provider.py',
            'config/agent_config.py',
            'simple_agent_dashboard.py',
            'monitor_automation.py',
            'requirements.txt',
            '.env.example'
        ]
        
        with open('DEPLOY_CHECKLIST.txt', 'w') as f:
            f.write("CLOUD DEPLOYMENT CHECKLIST\n")
            f.write("="*30 + "\n\n")
            f.write("1. Files to copy to cloud server:\n")
            for file in deploy_files:
                exists = "✅" if os.path.exists(file) else "❌"
                f.write(f"   {exists} {file}\n")
            f.write(f"\n2. Database export: {filename}\n")
            f.write("\n3. Environment variables needed:\n")
            f.write("   - DATABASE_URL\n")
            f.write("   - SUPABASE_URL\n")
            f.write("   - SUPABASE_ANON_KEY\n")
            f.write("   - DEEPSEEK_API_KEY\n")
            f.write("   - GEMINI_API_KEY\n")
            f.write("\n4. Commands to run on cloud server:\n")
            f.write("   git clone <your-repo>\n")
            f.write("   python3 -m venv venv_cloud\n")
            f.write("   source venv_cloud/bin/activate\n")
            f.write("   pip install -r requirements.txt\n")
            f.write("   python import_database.py\n")
            f.write("   python start_automation.py --continuous\n")
        
        print("✅ Created DEPLOY_CHECKLIST.txt")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Export error: {e}")


if __name__ == "__main__":
    asyncio.run(export_database())