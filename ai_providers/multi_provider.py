#!/usr/bin/env python3
"""
Multi-Provider AI Interface
Supports DeepSeek, Gemini, and OpenAI with automatic fallback
"""

import os
import json
import logging
import requests
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from dataclasses import dataclass
import google.generativeai as genai

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


@dataclass
class AIResponse:
    """Standardized AI response format"""
    content: str
    provider: str
    model: str
    success: bool
    error: Optional[str] = None
    usage: Optional[Dict] = None


class DeepSeekProvider:
    """DeepSeek AI provider"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.model = "deepseek-chat"
    
    def generate(self, prompt: str, system_prompt: str = None, temperature: float = 0.7) -> AIResponse:
        """Generate response from DeepSeek"""
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": 1000
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return AIResponse(
                    content=result['choices'][0]['message']['content'],
                    provider="DeepSeek",
                    model=self.model,
                    success=True,
                    usage=result.get('usage')
                )
            else:
                return AIResponse(
                    content="",
                    provider="DeepSeek",
                    model=self.model,
                    success=False,
                    error=f"API Error {response.status_code}: {response.text}"
                )
                
        except Exception as e:
            return AIResponse(
                content="",
                provider="DeepSeek",
                model=self.model,
                success=False,
                error=str(e)
            )


class GeminiProvider:
    """Google Gemini AI provider"""
    
    def __init__(self, api_key: str):
        genai.configure(api_key=api_key)
        self.model_name = "gemini-1.5-flash"  # Updated model name
        self.model = genai.GenerativeModel(self.model_name)
    
    def generate(self, prompt: str, system_prompt: str = None, temperature: float = 0.7) -> AIResponse:
        """Generate response from Gemini"""
        
        # Combine system prompt and user prompt for Gemini
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"
        
        generation_config = {
            "temperature": temperature,
            "top_p": 1,
            "top_k": 1,
            "max_output_tokens": 2048,
        }
        
        try:
            response = self.model.generate_content(
                full_prompt,
                generation_config=generation_config
            )
            
            return AIResponse(
                content=response.text,
                provider="Gemini",
                model=self.model_name,
                success=True
            )
            
        except Exception as e:
            return AIResponse(
                content="",
                provider="Gemini",
                model=self.model_name,
                success=False,
                error=str(e)
            )


class OpenAIProvider:
    """OpenAI provider (for when credits are available)"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.model = "gpt-3.5-turbo"
    
    def generate(self, prompt: str, system_prompt: str = None, temperature: float = 0.7) -> AIResponse:
        """Generate response from OpenAI"""
        try:
            from openai import OpenAI
            client = OpenAI(api_key=self.api_key)
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=1000
            )
            
            return AIResponse(
                content=response.choices[0].message.content,
                provider="OpenAI",
                model=self.model,
                success=True,
                usage=response.usage.model_dump() if response.usage else None
            )
            
        except Exception as e:
            return AIResponse(
                content="",
                provider="OpenAI",
                model=self.model,
                success=False,
                error=str(e)
            )


class MultiProviderAI:
    """Multi-provider AI interface with automatic fallback"""
    
    def __init__(self):
        self.providers = []
        
        # Initialize DeepSeek
        deepseek_key = os.getenv('DEEPSEEK_API_KEY')
        if deepseek_key:
            self.providers.append(DeepSeekProvider(deepseek_key))
            logger.info("DeepSeek provider initialized")
        
        # Initialize Gemini
        gemini_key = os.getenv('GEMINI_API_KEY')
        if gemini_key:
            self.providers.append(GeminiProvider(gemini_key))
            logger.info("Gemini provider initialized")
        
        # Initialize OpenAI (if available)
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            self.providers.append(OpenAIProvider(openai_key))
            logger.info("OpenAI provider initialized")
        
        if not self.providers:
            logger.warning("No AI providers configured!")
    
    def generate(self, prompt: str, system_prompt: str = None, 
                temperature: float = 0.7, preferred_provider: str = None) -> AIResponse:
        """Generate response with automatic fallback"""
        
        # Try preferred provider first
        if preferred_provider:
            for provider in self.providers:
                if provider.__class__.__name__.lower().startswith(preferred_provider.lower()):
                    response = provider.generate(prompt, system_prompt, temperature)
                    if response.success:
                        return response
                    logger.warning(f"{preferred_provider} failed: {response.error}")
        
        # Try all providers in order
        for provider in self.providers:
            try:
                response = provider.generate(prompt, system_prompt, temperature)
                if response.success:
                    return response
                logger.warning(f"{provider.__class__.__name__} failed: {response.error}")
            except Exception as e:
                logger.error(f"Provider {provider.__class__.__name__} error: {e}")
        
        # All providers failed
        return AIResponse(
            content="",
            provider="None",
            model="None",
            success=False,
            error="All AI providers failed"
        )
    
    def generate_hemp_products(self, plant_part: str, count: int = 5, 
                             innovative: bool = False, preferred_provider: str = None) -> List[Dict]:
        """Specialized method for generating hemp product ideas"""
        
        system_prompt = """You are a hemp industry expert with deep knowledge of hemp products, 
applications, and market trends. Provide detailed, accurate, and innovative product information."""
        
        if innovative:
            prompt = f"""Generate {count} cutting-edge, innovative hemp {plant_part} products that represent 
the latest technology and research. Include products that use advanced materials science, biotechnology, 
nanotechnology, or other frontier technologies.

For each product, provide this JSON structure:
[
  {{
    "name": "Specific product name",
    "description": "Detailed 2-3 sentence description with technical details",
    "industry": "Primary industry category",
    "sub_industry": "Specific sub-category",
    "benefits": ["Key benefit 1", "Key benefit 2", "Key benefit 3"],
    "technical_specs": "Important technical specifications",
    "innovation_level": "high/medium",
    "applications": ["Application 1", "Application 2"]
  }}
]"""
        else:
            prompt = f"""Generate {count} real hemp {plant_part} products that exist in the market today.
Focus on commercially available products with proven applications.

For each product, provide this JSON structure:
[
  {{
    "name": "Specific product name",
    "description": "Clear 2-3 sentence description",
    "industry": "Primary industry category",
    "sub_industry": "Specific sub-category", 
    "benefits": ["Key benefit 1", "Key benefit 2", "Key benefit 3"],
    "technical_specs": "Key specifications",
    "market_status": "commercial/emerging",
    "applications": ["Application 1", "Application 2"]
  }}
]"""
        
        response = self.generate(prompt, system_prompt, temperature=0.7, preferred_provider=preferred_provider)
        
        if response.success:
            try:
                # Try to parse JSON response
                products = json.loads(response.content)
                return products
            except json.JSONDecodeError:
                # If not valid JSON, try to extract structured data
                logger.warning("Response was not valid JSON, attempting to parse")
                return self._parse_text_response(response.content)
        
        return []
    
    def _parse_text_response(self, text: str) -> List[Dict]:
        """Parse non-JSON text response into structured data"""
        products = []
        # Basic parsing logic - can be enhanced
        lines = text.strip().split('\n')
        current_product = {}
        
        for line in lines:
            line = line.strip()
            if line.startswith('1.') or line.startswith('2.') or line.startswith('3.'):
                if current_product:
                    products.append(current_product)
                current_product = {'name': line[2:].strip()}
            elif ':' in line:
                key, value = line.split(':', 1)
                current_product[key.strip().lower()] = value.strip()
        
        if current_product:
            products.append(current_product)
        
        return products
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return [p.__class__.__name__.replace('Provider', '') for p in self.providers]


# Convenience function
def get_ai_client() -> MultiProviderAI:
    """Get configured multi-provider AI client"""
    return MultiProviderAI()


# Testing function
def test_providers():
    """Test all configured providers"""
    ai = get_ai_client()
    
    print(f"\nAvailable providers: {ai.get_available_providers()}")
    
    test_prompt = "List 3 innovative uses for hemp fiber in one sentence each."
    
    # Test each provider
    for provider_name in ai.get_available_providers():
        print(f"\nTesting {provider_name}...")
        response = ai.generate(test_prompt, preferred_provider=provider_name)
        
        if response.success:
            print(f"✅ {provider_name} Success:")
            print(f"   Response: {response.content[:200]}...")
        else:
            print(f"❌ {provider_name} Failed: {response.error}")


if __name__ == "__main__":
    test_providers()