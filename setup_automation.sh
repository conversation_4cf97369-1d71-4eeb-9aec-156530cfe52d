#!/bin/bash
# Setup script for Hemp Database Automation

echo "======================================"
echo "🤖 HEMP DATABASE AUTOMATION SETUP"
echo "======================================"
echo ""
echo "Choose your automation method:"
echo ""
echo "1. Screen/Tmux (Easiest - Recommended for testing)"
echo "2. Systemd Service (Best for production)"
echo "3. <PERSON><PERSON><PERSON> (Simple scheduling)"
echo "4. Supervisor (Alternative to systemd)"
echo ""
read -p "Select option (1-4): " choice

case $choice in
    1)
        echo ""
        echo "📺 SCREEN/TMUX SETUP"
        echo "===================="
        echo ""
        echo "Option A: Using screen (if installed):"
        echo "--------------------------------------"
        echo "1. Start a new screen session:"
        echo "   screen -S hemp-orchestrator"
        echo ""
        echo "2. Run the orchestrator:"
        echo "   source venv_dedup/bin/activate"
        echo "   python automated_orchestrator.py --daemon"
        echo ""
        echo "3. Detach from screen:"
        echo "   Press Ctrl+A, then D"
        echo ""
        echo "4. To reattach later:"
        echo "   screen -r hemp-orchestrator"
        echo ""
        echo "Option B: Using tmux (if installed):"
        echo "------------------------------------"
        echo "1. Start a new tmux session:"
        echo "   tmux new -s hemp-orchestrator"
        echo ""
        echo "2. Run the orchestrator:"
        echo "   source venv_dedup/bin/activate"
        echo "   python automated_orchestrator.py --daemon"
        echo ""
        echo "3. Detach from tmux:"
        echo "   Press Ctrl+B, then D"
        echo ""
        echo "4. To reattach later:"
        echo "   tmux attach -t hemp-orchestrator"
        ;;
        
    2)
        echo ""
        echo "🔧 SYSTEMD SERVICE SETUP"
        echo "========================"
        python automated_orchestrator.py --setup-systemd
        ;;
        
    3)
        echo ""
        echo "⏰ CRON SETUP"
        echo "============="
        python automated_orchestrator.py --setup-cron
        ;;
        
    4)
        echo ""
        echo "📦 SUPERVISOR SETUP"
        echo "==================="
        echo ""
        echo "1. Install supervisor (if not installed):"
        echo "   sudo apt-get install supervisor"
        echo ""
        echo "2. Create config file:"
        echo "   sudo nano /etc/supervisor/conf.d/hemp-orchestrator.conf"
        echo ""
        echo "3. Add this content:"
        echo "----------------------------------------"
        cat << EOF
[program:hemp-orchestrator]
command=$(which python) $(pwd)/automated_orchestrator.py --daemon
directory=$(pwd)
user=$USER
autostart=true
autorestart=true
stderr_logfile=/var/log/hemp-orchestrator.err.log
stdout_logfile=/var/log/hemp-orchestrator.out.log
environment=PATH="$(dirname $(which python)):%(ENV_PATH)s"
EOF
        echo "----------------------------------------"
        echo ""
        echo "4. Update and start supervisor:"
        echo "   sudo supervisorctl reread"
        echo "   sudo supervisorctl update"
        echo "   sudo supervisorctl start hemp-orchestrator"
        echo ""
        echo "5. Check status:"
        echo "   sudo supervisorctl status hemp-orchestrator"
        ;;
esac

echo ""
echo "======================================"
echo "✅ Setup instructions displayed!"
echo "======================================"