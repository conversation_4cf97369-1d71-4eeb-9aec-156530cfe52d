"""
Agent Configuration Management
Loads and validates configuration from environment variables
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str
    supabase_url: Optional[str] = None
    supabase_anon_key: Optional[str] = None
    supabase_service_key: Optional[str] = None


@dataclass
class AIProviderConfig:
    """AI provider configuration"""
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    huggingface_api_key: Optional[str] = None
    default_provider: str = "openai"


@dataclass
class AgentConfig:
    """General agent configuration"""
    log_level: str = "INFO"
    max_concurrent_tasks: int = 5
    default_timeout: int = 300
    retry_attempts: int = 3
    rate_limit: int = 60


@dataclass
class ResearchAgentConfig:
    """Research agent specific configuration"""
    enabled: bool = True
    max_results: int = 50
    search_depth: int = 3
    verify_sources: bool = True
    pubmed_api_key: Optional[str] = None
    arxiv_api_key: Optional[str] = None


@dataclass
class ContentAgentConfig:
    """Content agent specific configuration"""
    enabled: bool = True
    min_words: int = 500
    max_words: int = 2000
    style: str = "professional"


@dataclass
class PlantPartAgentConfig:
    """Plant part agent specific configuration"""
    enabled: bool = True
    products_per_run: int = 20
    confidence_threshold: float = 0.7


@dataclass
class MonitoringConfig:
    """Monitoring configuration"""
    enabled: bool = True
    alert_email: Optional[str] = None
    slack_webhook_url: Optional[str] = None
    error_threshold: int = 5


class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.database = self._load_database_config()
        self.ai_providers = self._load_ai_provider_config()
        self.agent = self._load_agent_config()
        self.research_agent = self._load_research_agent_config()
        self.content_agent = self._load_content_agent_config()
        self.plant_part_agent = self._load_plant_part_agent_config()
        self.monitoring = self._load_monitoring_config()
        
        # Validate configuration
        self._validate_config()
    
    def _load_database_config(self) -> DatabaseConfig:
        """Load database configuration"""
        return DatabaseConfig(
            url=os.getenv('DATABASE_URL', ''),
            supabase_url=os.getenv('SUPABASE_URL'),
            supabase_anon_key=os.getenv('SUPABASE_ANON_KEY'),
            supabase_service_key=os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        )
    
    def _load_ai_provider_config(self) -> AIProviderConfig:
        """Load AI provider configuration"""
        return AIProviderConfig(
            openai_api_key=os.getenv('OPENAI_API_KEY'),
            anthropic_api_key=os.getenv('ANTHROPIC_API_KEY'),
            google_api_key=os.getenv('GOOGLE_API_KEY'),
            huggingface_api_key=os.getenv('HUGGINGFACE_API_KEY'),
            default_provider=os.getenv('DEFAULT_AI_PROVIDER', 'openai')
        )
    
    def _load_agent_config(self) -> AgentConfig:
        """Load general agent configuration"""
        return AgentConfig(
            log_level=os.getenv('AGENT_LOG_LEVEL', 'INFO'),
            max_concurrent_tasks=int(os.getenv('AGENT_MAX_CONCURRENT_TASKS', '5')),
            default_timeout=int(os.getenv('AGENT_DEFAULT_TIMEOUT', '300')),
            retry_attempts=int(os.getenv('AGENT_RETRY_ATTEMPTS', '3')),
            rate_limit=int(os.getenv('AGENT_RATE_LIMIT', '60'))
        )
    
    def _load_research_agent_config(self) -> ResearchAgentConfig:
        """Load research agent configuration"""
        return ResearchAgentConfig(
            enabled=os.getenv('RESEARCH_AGENT_ENABLED', 'true').lower() == 'true',
            max_results=int(os.getenv('RESEARCH_AGENT_MAX_RESULTS', '50')),
            search_depth=int(os.getenv('RESEARCH_AGENT_SEARCH_DEPTH', '3')),
            verify_sources=os.getenv('RESEARCH_AGENT_VERIFY_SOURCES', 'true').lower() == 'true',
            pubmed_api_key=os.getenv('PUBMED_API_KEY'),
            arxiv_api_key=os.getenv('ARXIV_API_KEY')
        )
    
    def _load_content_agent_config(self) -> ContentAgentConfig:
        """Load content agent configuration"""
        return ContentAgentConfig(
            enabled=os.getenv('CONTENT_AGENT_ENABLED', 'true').lower() == 'true',
            min_words=int(os.getenv('CONTENT_AGENT_MIN_WORDS', '500')),
            max_words=int(os.getenv('CONTENT_AGENT_MAX_WORDS', '2000')),
            style=os.getenv('CONTENT_AGENT_STYLE', 'professional')
        )
    
    def _load_plant_part_agent_config(self) -> PlantPartAgentConfig:
        """Load plant part agent configuration"""
        return PlantPartAgentConfig(
            enabled=os.getenv('PLANT_PART_AGENT_ENABLED', 'true').lower() == 'true',
            products_per_run=int(os.getenv('PLANT_PART_AGENT_PRODUCTS_PER_RUN', '20')),
            confidence_threshold=float(os.getenv('PLANT_PART_AGENT_CONFIDENCE_THRESHOLD', '0.7'))
        )
    
    def _load_monitoring_config(self) -> MonitoringConfig:
        """Load monitoring configuration"""
        return MonitoringConfig(
            enabled=os.getenv('MONITORING_ENABLED', 'true').lower() == 'true',
            alert_email=os.getenv('ALERT_EMAIL'),
            slack_webhook_url=os.getenv('SLACK_WEBHOOK_URL'),
            error_threshold=int(os.getenv('ERROR_THRESHOLD', '5'))
        )
    
    def _validate_config(self):
        """Validate configuration"""
        errors = []
        
        # Check database configuration
        if not self.database.url:
            errors.append("DATABASE_URL is required")
        
        # Check AI provider configuration
        if not any([
            self.ai_providers.openai_api_key,
            self.ai_providers.anthropic_api_key,
            self.ai_providers.huggingface_api_key
        ]):
            errors.append("At least one AI provider API key is required")
        
        # Log validation results
        if errors:
            for error in errors:
                logger.error(f"Configuration error: {error}")
            logger.warning("Configuration validation failed. Some features may not work.")
        else:
            logger.info("Configuration validated successfully")
    
    def get_ai_provider_key(self, provider: Optional[str] = None) -> Optional[str]:
        """Get API key for specified provider or default"""
        provider = provider or self.ai_providers.default_provider
        
        provider_map = {
            'openai': self.ai_providers.openai_api_key,
            'anthropic': self.ai_providers.anthropic_api_key,
            'google': self.ai_providers.google_api_key,
            'huggingface': self.ai_providers.huggingface_api_key
        }
        
        return provider_map.get(provider)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'database': {
                'has_url': bool(self.database.url),
                'has_supabase': bool(self.database.supabase_url)
            },
            'ai_providers': {
                'has_openai': bool(self.ai_providers.openai_api_key),
                'has_anthropic': bool(self.ai_providers.anthropic_api_key),
                'has_google': bool(self.ai_providers.google_api_key),
                'has_huggingface': bool(self.ai_providers.huggingface_api_key),
                'default': self.ai_providers.default_provider
            },
            'agent': {
                'log_level': self.agent.log_level,
                'max_concurrent_tasks': self.agent.max_concurrent_tasks,
                'timeout': self.agent.default_timeout,
                'retry_attempts': self.agent.retry_attempts,
                'rate_limit': self.agent.rate_limit
            },
            'agents_enabled': {
                'research': self.research_agent.enabled,
                'content': self.content_agent.enabled,
                'plant_part': self.plant_part_agent.enabled
            },
            'monitoring': {
                'enabled': self.monitoring.enabled,
                'has_email': bool(self.monitoring.alert_email),
                'has_slack': bool(self.monitoring.slack_webhook_url)
            }
        }


# Singleton instance
config = Config()


# Helper functions
def get_config() -> Config:
    """Get configuration instance"""
    return config


def validate_ai_provider(provider: str) -> bool:
    """Check if AI provider is configured"""
    return bool(config.get_ai_provider_key(provider))


def get_database_url() -> str:
    """Get database URL"""
    return config.database.url


def is_agent_enabled(agent_type: str) -> bool:
    """Check if specific agent is enabled"""
    agent_map = {
        'research': config.research_agent.enabled,
        'content': config.content_agent.enabled,
        'plant_part': config.plant_part_agent.enabled
    }
    return agent_map.get(agent_type, False)