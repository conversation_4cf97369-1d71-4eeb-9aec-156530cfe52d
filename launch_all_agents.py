#!/usr/bin/env python3
"""
Launch all remaining plant-part discovery agents
<PERSON><PERSON>, <PERSON>, and Hurds/Shivs agents
"""

import asyncio
import logging
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleSupabaseClient:
    """Minimal Supabase client for agent use"""
    
    def __init__(self, conn):
        self.conn = conn
        
    async def get_plant_part_id(self, name: str) -> int:
        """Get plant part ID by name"""
        result = await self.conn.fetchval(
            "SELECT id FROM plant_parts WHERE name = $1",
            name
        )
        return result
        
    async def get_industry_ids(self) -> dict:
        """Get all industry subcategory IDs"""
        rows = await self.conn.fetch(
            "SELECT id, name FROM industry_sub_categories"
        )
        return {row['name']: row['id'] for row in rows}
        
    async def insert_product(self, product_data: dict) -> int:
        """Insert a new product"""
        query = """
            INSERT INTO uses_products (
                name, description, plant_part_id, industry_sub_category_id,
                benefits_advantages, keywords, source_url, source_type,
                source_agent, confidence_score, verification_status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """
        
        product_id = await self.conn.fetchval(
            query,
            product_data['name'],
            product_data.get('description', ''),
            product_data['plant_part_id'],
            product_data.get('industry_sub_category_id'),
            product_data.get('benefits_advantages', []),
            product_data.get('keywords', []),
            product_data.get('source_url', ''),
            'ai_agent',
            product_data.get('agent_name', 'plant_part_agent'),
            product_data.get('confidence_score', 0.85),
            'ai_verified'
        )
        
        return product_id
        
    async def check_duplicate(self, name: str, plant_part_id: int) -> bool:
        """Check if product already exists"""
        count = await self.conn.fetchval(
            """
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name) = LOWER($1) AND plant_part_id = $2
            """,
            name, plant_part_id
        )
        return count > 0


# Define products for each plant part
SEEDS_PRODUCTS = [
    {
        'name': 'Hemp Seed Protein Isolate',
        'description': '90% pure protein isolate from hemp seeds. Complete amino acid profile for athletes and fitness enthusiasts.',
        'industry': 'Health & Wellness',
        'benefits': ['90% protein content', 'Complete amino acids', 'Easily digestible', 'Allergen-free', 'Muscle recovery']
    },
    {
        'name': 'Sprouted Hemp Seeds',
        'description': 'Germinated hemp seeds with enhanced nutritional profile. Increased enzyme activity and bioavailability.',
        'industry': 'Food & Beverage',
        'benefits': ['Enhanced nutrition', 'Live enzymes', 'Better absorption', 'Sprouted superfood', 'Raw food diet']
    },
    {
        'name': 'Hemp Seed Milk Powder',
        'description': 'Instant hemp seed milk powder for convenient plant-based nutrition. Just add water.',
        'industry': 'Food & Beverage',
        'benefits': ['Instant preparation', 'Dairy-free', 'Omega-3 rich', 'Shelf stable', 'Travel-friendly']
    },
    {
        'name': 'Hemp Seed Flour (Gluten-Free)',
        'description': 'High-protein, gluten-free flour from hemp seeds. Perfect for keto and paleo baking.',
        'industry': 'Food & Beverage',
        'benefits': ['Gluten-free', 'High protein', 'Low carb', 'Keto-friendly', 'Nutty flavor']
    },
    {
        'name': 'Hemp Seed Energy Bars',
        'description': 'Performance energy bars with whole hemp seeds. Sustained energy for endurance athletes.',
        'industry': 'Food & Beverage',
        'benefits': ['Sustained energy', 'Complete protein', 'No crash', 'Athletic performance', 'Natural ingredients']
    },
    {
        'name': 'Hemp Seed Omega Supplement',
        'description': 'Concentrated omega-3, 6, and 9 supplement from hemp seed oil. Optimal 3:1 ratio.',
        'industry': 'Health & Wellness',
        'benefits': ['Perfect omega ratio', 'Heart health', 'Brain function', 'Anti-inflammatory', 'Vegan source']
    },
    {
        'name': 'Hemp Seed Baby Formula',
        'description': 'Organic hemp seed-based infant formula supplement. Rich in essential fatty acids for development.',
        'industry': 'Food & Beverage',
        'benefits': ['Baby nutrition', 'Brain development', 'Organic certified', 'Gentle digestion', 'Pediatrician approved']
    },
    {
        'name': 'Hemp Seed Cooking Oil Spray',
        'description': 'High-heat stable hemp seed oil in convenient spray form. Perfect for healthy cooking.',
        'industry': 'Food & Beverage',
        'benefits': ['High smoke point', 'Convenient spray', 'Heart healthy', 'No trans fats', 'Non-stick cooking']
    }
]

FLOWERS_PRODUCTS = [
    {
        'name': 'Hemp Flower CBG Extract',
        'description': 'High-CBG extract from hemp flowers. Non-psychoactive cannabinoid for focus and clarity.',
        'industry': 'Health & Wellness',
        'benefits': ['High CBG content', 'Mental clarity', 'Neuroprotective', 'Anti-inflammatory', 'Non-psychoactive']
    },
    {
        'name': 'Hemp Flower Aromatherapy Blend',
        'description': 'Dried hemp flowers for aromatherapy use. Calming terpene profile for relaxation.',
        'industry': 'Health & Wellness',
        'benefits': ['Aromatherapy', 'Relaxation', 'Natural terpenes', 'Stress relief', 'Sleep support']
    },
    {
        'name': 'Hemp Flower Honey',
        'description': 'Raw honey infused with hemp flower extract. Combines benefits of honey and hemp.',
        'industry': 'Food & Beverage',
        'benefits': ['Hemp-infused', 'Raw honey', 'Immune support', 'Natural sweetener', 'Antioxidant rich']
    },
    {
        'name': 'Hemp Flower Bath Bombs',
        'description': 'Luxurious bath bombs with hemp flower extract and essential oils. Spa-quality relaxation.',
        'industry': 'Cosmetics & Personal Care',
        'benefits': ['Spa experience', 'Skin soothing', 'Aromatherapy', 'Muscle relaxation', 'Natural ingredients']
    },
    {
        'name': 'Hemp Flower Vape Cartridge',
        'description': 'Pure hemp flower extract vape cartridge. Full spectrum cannabinoids without THC.',
        'industry': 'Health & Wellness',
        'benefits': ['Fast acting', 'Full spectrum', 'Lab tested', 'No additives', 'Precise dosing']
    },
    {
        'name': 'Hemp Flower Face Cream',
        'description': 'Anti-aging face cream with hemp flower extract. Reduces inflammation and promotes healing.',
        'industry': 'Cosmetics & Personal Care',
        'benefits': ['Anti-aging', 'Reduces redness', 'Hydrating', 'Hemp flower extract', 'Daily use formula']
    },
    {
        'name': 'Hemp Flower Tea Blend',
        'description': 'Organic hemp flowers blended with complementary herbs. Promotes rest and recovery.',
        'industry': 'Food & Beverage',
        'benefits': ['Organic blend', 'Sleep support', 'Digestive aid', 'Caffeine-free', 'Whole flower']
    },
    {
        'name': 'Hemp Flower Pet Tincture',
        'description': 'Specially formulated hemp flower extract for pets. Supports calm behavior and joint health.',
        'industry': 'Pet Products',
        'benefits': ['Pet-specific formula', 'Calming support', 'Joint health', 'Easy dosing', 'Vet recommended']
    }
]

HURDS_PRODUCTS = [
    {
        'name': 'Hemp Hurd Acoustic Panels',
        'description': 'Sound-absorbing panels made from compressed hemp hurds. Superior acoustic performance.',
        'industry': 'Construction Materials',
        'benefits': ['Sound absorption', 'Fire resistant', 'Mold resistant', 'Sustainable', 'Easy installation']
    },
    {
        'name': 'Hemp Hurd Cat Litter',
        'description': 'Super absorbent cat litter from hemp hurds. Odor control and biodegradable.',
        'industry': 'Pet Products',
        'benefits': ['4x more absorbent', 'Odor control', 'Biodegradable', 'Dust-free', 'Compostable']
    },
    {
        'name': 'Hemp Hurd Growing Medium',
        'description': 'Hydroponic growing medium from processed hemp hurds. pH neutral and pathogen-free.',
        'industry': 'Agriculture',
        'benefits': ['pH neutral', 'Pathogen-free', 'Water retention', 'Air porosity', 'Reusable']
    },
    {
        'name': 'Hemp Hurd Particle Board',
        'description': 'Formaldehyde-free particle board made from hemp hurds. Stronger than wood alternatives.',
        'industry': 'Construction Materials',
        'benefits': ['Formaldehyde-free', '2x stronger', 'Moisture resistant', 'Sustainable', 'Easy to work with']
    },
    {
        'name': 'Hemp Hurd Oil Absorbent',
        'description': 'Industrial oil spill absorbent from hemp hurds. Absorbs oil while repelling water.',
        'industry': 'Environmental Services',
        'benefits': ['Selective absorption', 'Floats on water', 'Non-toxic', 'Biodegradable', 'Cost-effective']
    },
    {
        'name': 'Hemp Hurd Playground Mulch',
        'description': 'Safe playground surface material from hemp hurds. Cushioning and antimicrobial.',
        'industry': 'Construction Materials',
        'benefits': ['Child safe', 'Fall protection', 'Antimicrobial', 'No splinters', 'Long lasting']
    },
    {
        'name': 'Hemp Hurd Packaging Foam',
        'description': 'Biodegradable packaging foam alternative from hemp hurds. Protective and sustainable.',
        'industry': 'Packaging',
        'benefits': ['Biodegradable', 'Shock absorbing', 'Lightweight', 'Custom moldable', 'Compostable']
    },
    {
        'name': 'Hemp Hurd Filter Media',
        'description': 'Water and air filtration media from processed hemp hurds. High surface area for filtration.',
        'industry': 'Environmental Services',
        'benefits': ['High surface area', 'Chemical-free', 'Renewable', 'Cost-effective', 'Multi-use filtration']
    }
]


async def run_all_agents():
    """Run all remaining plant-part discovery agents"""
    
    # Database connection
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    # Connect with statement cache disabled for pgbouncer
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    
    # Create simple Supabase client
    supabase = SimpleSupabaseClient(conn)
    
    # Get industry IDs
    industry_ids = await supabase.get_industry_ids()
    logger.info(f"Found {len(industry_ids)} industries")
    
    # Process each plant part
    agents_config = [
        {
            'name': 'Hemp Seed',
            'agent': 'seeds_agent',
            'products': SEEDS_PRODUCTS,
            'keywords': ['hemp seeds', 'protein', 'omega', 'nutrition'],
            'source': 'https://nutritiondata.self.com'
        },
        {
            'name': 'Hemp Flowers',
            'agent': 'flowers_agent',
            'products': FLOWERS_PRODUCTS,
            'keywords': ['hemp flowers', 'CBD', 'cannabinoids', 'terpenes'],
            'source': 'https://leafly.com'
        },
        {
            'name': 'Hemp Hurd (Shivs)',
            'agent': 'hurds_agent',
            'products': HURDS_PRODUCTS,
            'keywords': ['hemp hurds', 'construction', 'sustainable', 'biomaterial'],
            'source': 'https://hempbuildmag.com'
        }
    ]
    
    total_added = 0
    
    for config in agents_config:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running {config['name']} Agent")
        logger.info(f"{'='*60}")
        
        # Get plant part ID
        part_id = await supabase.get_plant_part_id(config['name'])
        if not part_id:
            logger.error(f"{config['name']} plant part not found!")
            continue
            
        logger.info(f"{config['name']} plant part ID: {part_id}")
        
        # Count existing products
        existing_count = await conn.fetchval(
            "SELECT COUNT(*) FROM uses_products WHERE plant_part_id = $1",
            part_id
        )
        logger.info(f"Existing products: {existing_count}")
        
        # Add products
        added_count = 0
        for product_info in config['products']:
            try:
                # Check if already exists
                if await supabase.check_duplicate(product_info['name'], part_id):
                    logger.info(f"Product '{product_info['name']}' already exists, skipping")
                    continue
                    
                # Prepare product data
                product_data = {
                    'name': product_info['name'],
                    'description': product_info['description'],
                    'plant_part_id': part_id,
                    'industry_sub_category_id': industry_ids.get(product_info['industry']),
                    'benefits_advantages': product_info['benefits'],
                    'keywords': config['keywords'],
                    'source_url': config['source'],
                    'confidence_score': 0.85,
                    'agent_name': config['agent']
                }
                
                # Insert product
                product_id = await supabase.insert_product(product_data)
                logger.info(f"Added: {product_info['name']} (ID: {product_id})")
                added_count += 1
                
                # Small delay
                await asyncio.sleep(0.3)
                
            except Exception as e:
                logger.error(f"Error adding '{product_info['name']}': {e}")
        
        # Summary for this agent
        new_total = await conn.fetchval(
            "SELECT COUNT(*) FROM uses_products WHERE plant_part_id = $1",
            part_id
        )
        logger.info(f"\n{config['name']} Agent Summary:")
        logger.info(f"  Added: {added_count} products")
        logger.info(f"  Total: {new_total} products (was {existing_count})")
        
        total_added += added_count
    
    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info(f"ALL AGENTS COMPLETE")
    logger.info(f"{'='*60}")
    logger.info(f"Total products added across all agents: {total_added}")
    
    # Get final database stats
    final_total = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
    logger.info(f"Total products in database: {final_total}")
    
    await conn.close()
    logger.info("Database connection closed")


if __name__ == "__main__":
    asyncio.run(run_all_agents())