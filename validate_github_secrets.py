#!/usr/bin/env python3
"""
GitHub Secrets Validation Script
Helps verify that all required secrets are properly configured
"""

import os
import sys
import json
import requests
from datetime import datetime

def check_github_secrets_via_api():
    """Check GitHub secrets via API (shows names only, not values)"""
    print("🔍 Checking GitHub Repository Secrets...")
    
    try:
        # This would require a GitHub token with repo access
        # For now, we'll just show what should be there
        expected_secrets = [
            'SUPABASE_URL',
            'SUPABASE_ANON_KEY', 
            'DEEPSEEK_API_KEY',
            'GEMINI_API_KEY',
            'OPENAI_API_KEY'
        ]
        
        print("📋 Expected GitHub Secrets:")
        for secret in expected_secrets:
            print(f"   - {secret}")
        
        print("\n💡 Note: Secret values are hidden for security")
        print("   You can verify they exist in GitHub Settings > Secrets and variables > Actions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking secrets: {e}")
        return False

def test_api_key_format(key_name, key_value, expected_prefix=None):
    """Test if an API key has the expected format"""
    if not key_value:
        return False, "Key is empty or not set"
    
    if len(key_value) < 10:
        return False, "Key is too short"
    
    if expected_prefix and not key_value.startswith(expected_prefix):
        return False, f"Key should start with '{expected_prefix}'"
    
    return True, "Format looks correct"

def test_deepseek_api_key():
    """Test DeepSeek API key format and basic connectivity"""
    print("\n🤖 Testing DeepSeek API Key...")
    
    api_key = os.environ.get('DEEPSEEK_API_KEY')
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not found in environment")
        print("   Add it to GitHub Secrets: Settings > Secrets and variables > Actions")
        return False
    
    # Test format
    is_valid, message = test_api_key_format('DEEPSEEK_API_KEY', api_key, 'sk-')
    if not is_valid:
        print(f"❌ DeepSeek API key format issue: {message}")
        return False
    
    print(f"✅ DeepSeek API key format: {message}")
    print(f"   Key length: {len(api_key)} characters")
    print(f"   Starts with: {api_key[:8]}...")
    
    # Test basic connectivity (optional)
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # Simple test request
        response = requests.get(
            'https://api.deepseek.com/v1/models',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ DeepSeek API connection successful")
            return True
        elif response.status_code == 401:
            print("❌ DeepSeek API key authentication failed")
            print("   Please check your API key is correct and has credits")
            return False
        else:
            print(f"⚠️ DeepSeek API returned status {response.status_code}")
            print("   Key format is correct, but API test inconclusive")
            return True
            
    except requests.exceptions.RequestException as e:
        print(f"⚠️ DeepSeek API connectivity test failed: {e}")
        print("   This might be a network issue - key format is correct")
        return True
    except Exception as e:
        print(f"⚠️ DeepSeek API test error: {e}")
        return True

def test_gemini_api_key():
    """Test Gemini API key format and basic connectivity"""
    print("\n🧠 Testing Gemini API Key...")
    
    api_key = os.environ.get('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment")
        print("   Add it to GitHub Secrets: Settings > Secrets and variables > Actions")
        return False
    
    # Test format
    is_valid, message = test_api_key_format('GEMINI_API_KEY', api_key)
    if not is_valid:
        print(f"❌ Gemini API key format issue: {message}")
        return False
    
    print(f"✅ Gemini API key format: {message}")
    print(f"   Key length: {len(api_key)} characters")
    print(f"   Starts with: {api_key[:8]}...")
    
    # Test basic connectivity (optional)
    try:
        url = f'https://generativelanguage.googleapis.com/v1beta/models?key={api_key}'
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Gemini API connection successful")
            return True
        elif response.status_code == 400:
            print("❌ Gemini API key authentication failed")
            print("   Please check your API key is correct")
            return False
        else:
            print(f"⚠️ Gemini API returned status {response.status_code}")
            print("   Key format is correct, but API test inconclusive")
            return True
            
    except requests.exceptions.RequestException as e:
        print(f"⚠️ Gemini API connectivity test failed: {e}")
        print("   This might be a network issue - key format is correct")
        return True
    except Exception as e:
        print(f"⚠️ Gemini API test error: {e}")
        return True

def test_openai_api_key():
    """Test OpenAI API key (existing)"""
    print("\n🔮 Testing OpenAI API Key...")
    
    api_key = os.environ.get('OPENAI_API_KEY')
    if not api_key:
        print("⚠️ OPENAI_API_KEY not found (optional fallback)")
        return True
    
    # Test format
    is_valid, message = test_api_key_format('OPENAI_API_KEY', api_key, 'sk-')
    if not is_valid:
        print(f"❌ OpenAI API key format issue: {message}")
        return False
    
    print(f"✅ OpenAI API key format: {message}")
    print(f"   Key length: {len(api_key)} characters")
    print(f"   Starts with: {api_key[:8]}...")
    print("   (Will be used as fallback only)")
    
    return True

def generate_setup_report():
    """Generate a comprehensive setup validation report"""
    print("\n📊 Generating Setup Validation Report...")
    
    # Run all tests
    github_ok = check_github_secrets_via_api()
    deepseek_ok = test_deepseek_api_key()
    gemini_ok = test_gemini_api_key()
    openai_ok = test_openai_api_key()
    
    # Calculate results
    total_tests = 4
    passed_tests = sum([github_ok, deepseek_ok, gemini_ok, openai_ok])
    
    # Determine readiness
    critical_tests = [deepseek_ok]  # DeepSeek is most critical
    system_ready = all(critical_tests)
    
    # Create report
    report = {
        'validation_timestamp': datetime.now().isoformat(),
        'system_ready': system_ready,
        'tests_passed': passed_tests,
        'total_tests': total_tests,
        'test_results': {
            'github_secrets': github_ok,
            'deepseek_api': deepseek_ok,
            'gemini_api': gemini_ok,
            'openai_api': openai_ok
        },
        'provider_priority': [
            {'name': 'DeepSeek', 'status': 'available' if deepseek_ok else 'missing', 'cost': '$0.0014/1K tokens'},
            {'name': 'Gemini', 'status': 'available' if gemini_ok else 'missing', 'cost': '$0.0075/1K tokens'},
            {'name': 'OpenAI', 'status': 'available' if openai_ok else 'missing', 'cost': '$0.03/1K tokens'}
        ],
        'recommendations': []
    }
    
    # Add recommendations
    if not deepseek_ok:
        report['recommendations'].append("CRITICAL: Add DEEPSEEK_API_KEY to GitHub Secrets for lowest cost operations")
    
    if not gemini_ok:
        report['recommendations'].append("RECOMMENDED: Add GEMINI_API_KEY to GitHub Secrets for backup operations")
    
    if not openai_ok:
        report['recommendations'].append("OPTIONAL: OPENAI_API_KEY available as fallback (highest cost)")
    
    # Save report
    os.makedirs('reports', exist_ok=True)
    with open('reports/github-secrets-validation.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    return report

def main():
    """Main validation function"""
    print("🔐 GitHub Secrets Validation for Multi-Provider AI System")
    print("=" * 60)
    
    # Generate comprehensive report
    report = generate_setup_report()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    
    if report['system_ready']:
        print("🎉 SYSTEM READY FOR PRODUCTION!")
        print("Your multi-provider AI system is properly configured.")
        print("\nNext steps:")
        print("1. Run a test workflow: Actions > Automated Hemp Database Operations")
        print("2. Monitor AI costs in the generated reports")
        print("3. Verify DeepSeek is being used as primary provider")
    else:
        print("⚠️ SETUP INCOMPLETE")
        print("Please address the issues above before using the AI system.")
        
        if report['recommendations']:
            print("\n🔧 Action Items:")
            for i, rec in enumerate(report['recommendations'], 1):
                print(f"{i}. {rec}")
    
    print(f"\nValidation Results: {report['tests_passed']}/{report['total_tests']} tests passed")
    print("📄 Detailed report saved to: reports/github-secrets-validation.json")
    
    # Show provider status
    print("\n🤖 AI Provider Status:")
    for provider in report['provider_priority']:
        status_icon = "✅" if provider['status'] == 'available' else "❌"
        print(f"   {status_icon} {provider['name']}: {provider['status']} ({provider['cost']})")
    
    return report['system_ready']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
