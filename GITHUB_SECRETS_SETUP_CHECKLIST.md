# 🔐 GitHub Secrets Setup Checklist

## ✅ Current Status
You already have these secrets configured:
- ✅ `SUPABASE_URL` (configured)
- ✅ `SUPABASE_ANON_KEY` (configured)
- ✅ `OPENAI_API_KEY` (configured - will be fallback only)

## 🆕 Required New Secrets

### **Step 1: Get DeepSeek API Key** (CRITICAL - Lowest Cost)
1. **Visit**: [https://platform.deepseek.com/](https://platform.deepseek.com/)
2. **Sign up** for a new account or **sign in**
3. **Navigate** to API Keys section
4. **Create** a new API key
5. **Copy** the key (starts with `sk-`)
6. **Keep secure** - you'll add this to GitHub

### **Step 2: Get Gemini API Key** (RECOMMENDED - Backup)
1. **Visit**: [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
2. **Sign in** with your Google account
3. **Click** "Create API Key"
4. **Select** existing project or create new one
5. **Copy** the generated API key
6. **Keep secure** - you'll add this to GitHub

### **Step 3: Add Secrets to GitHub**
1. **Go to**: [https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/settings/secrets/actions](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/settings/secrets/actions)
2. **Add DeepSeek Key**:
   - Click "New repository secret"
   - Name: `DEEPSEEK_API_KEY`
   - Secret: [Paste your DeepSeek key]
   - Click "Add secret"
3. **Add Gemini Key**:
   - Click "New repository secret"
   - Name: `GEMINI_API_KEY`
   - Secret: [Paste your Gemini key]
   - Click "Add secret"

### **Step 4: Verify Setup**
Run this command to validate your setup:
```bash
python validate_github_secrets.py
```

### **Step 5: Test the System**
1. **Go to**: [Actions tab](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/actions)
2. **Click**: "Automated Hemp Database Operations"
3. **Click**: "Run workflow"
4. **Configure**:
   - Operation: `discovery`
   - Max items: `5`
   - Test mode: `true`
5. **Click**: "Run workflow"

## 🎯 Expected Results

### ✅ Success Indicators
- Workflow completes with green checkmarks
- Logs show: "Available AI providers: DeepSeek, Gemini"
- Logs show: "Selected provider: DeepSeek ($0.0014/1K tokens)"
- AI cost in reports: ~$0.001-0.005 per run
- Enhanced product descriptions generated

### ❌ Failure Indicators
- "Missing required secrets" error
- "No AI providers available" warning
- API authentication errors
- High costs (indicates OpenAI fallback usage)

## 💰 Cost Expectations

### With Proper Setup (DeepSeek Primary)
- **Per Operation**: $0.001-0.005
- **Daily**: $0.10-0.30
- **Monthly**: $3-10

### If Only OpenAI Available (Fallback)
- **Per Operation**: $0.05-0.15
- **Daily**: $2-5
- **Monthly**: $60-150

## 🆘 Troubleshooting

### "DEEPSEEK_API_KEY not found"
- **Solution**: Add the secret in GitHub repository settings
- **URL**: Settings > Secrets and variables > Actions

### "DeepSeek API key authentication failed"
- **Check**: API key is correct and has credits
- **Verify**: Key starts with `sk-` and is complete

### "Selected provider: OpenAI"
- **Meaning**: DeepSeek and Gemini are not available
- **Action**: Check API keys are properly configured

### High AI costs
- **Check**: Which provider is being used in logs
- **Ensure**: DeepSeek is working (lowest cost)
- **Review**: AI usage reports for cost breakdown

## 📞 Quick Links

- **GitHub Secrets**: [Repository Settings](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/settings/secrets/actions)
- **DeepSeek Platform**: [https://platform.deepseek.com/](https://platform.deepseek.com/)
- **Google AI Studio**: [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
- **GitHub Actions**: [Workflow Runs](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/actions)

## 🎉 Success Checklist

- [ ] DeepSeek API key obtained
- [ ] Gemini API key obtained (optional but recommended)
- [ ] `DEEPSEEK_API_KEY` added to GitHub Secrets
- [ ] `GEMINI_API_KEY` added to GitHub Secrets
- [ ] Validation script passes all tests
- [ ] Test workflow run successful
- [ ] AI costs under $0.01 per run
- [ ] Enhanced product descriptions generated

---

**Once complete, your Hemp Database will generate professional AI content at 95% lower cost!**
