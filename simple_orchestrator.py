#!/usr/bin/env python3
"""
Simple Agent Orchestrator - Runs agents on a schedule without complex dependencies
"""

import os
import asyncio
import logging
import subprocess
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Agent configuration
AGENTS = {
    'cannabinoids': {
        'script': 'launch_cannabinoids_agent.py',
        'schedule': 'daily',
        'priority': 'high',
        'description': 'Discovers cannabinoid products'
    },
    'terpenes': {
        'script': 'launch_terpenes_agent.py',
        'schedule': 'daily',
        'priority': 'high',
        'description': 'Discovers terpene products'
    },
    'roots': {
        'script': 'launch_roots_agent.py',
        'schedule': 'daily',
        'priority': 'medium',
        'description': 'Discovers root-based products'
    },
    'leaves': {
        'script': 'launch_leaves_agent.py',
        'schedule': 'daily',
        'priority': 'medium',
        'description': 'Discovers leaf-based products'
    },
    'innovation': {
        'script': 'run_innovation_search.py',
        'schedule': 'weekly',
        'priority': 'high',
        'description': 'Finds cutting-edge innovations'
    },
    'sustainability': {
        'script': 'run_sustainability_agent.py',
        'schedule': 'weekly',
        'priority': 'medium',
        'description': 'Discovers eco-friendly solutions'
    }
}

# Track last run times
last_run = {}


async def run_agent(agent_name: str, agent_config: dict):
    """Run a single agent"""
    logger.info(f"🤖 Running {agent_name} agent...")
    
    try:
        # Run the agent script
        result = subprocess.run(
            [sys.executable, agent_config['script']],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {agent_name} agent completed successfully")
            # Parse output for metrics if available
            output_lines = result.stdout.strip().split('\n')
            for line in output_lines[-5:]:  # Check last 5 lines for summary
                if 'Products added:' in line or 'Total' in line:
                    logger.info(f"   {line.strip()}")
        else:
            logger.error(f"❌ {agent_name} agent failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        logger.error(f"⏱️ {agent_name} agent timed out")
    except FileNotFoundError:
        logger.error(f"📁 {agent_name} agent script not found: {agent_config['script']}")
    except Exception as e:
        logger.error(f"❌ Error running {agent_name} agent: {e}")


async def should_run_agent(agent_name: str, schedule: str) -> bool:
    """Check if agent should run based on schedule"""
    current_time = datetime.now()
    
    if agent_name not in last_run:
        # First run
        return True
    
    last_run_time = last_run[agent_name]
    
    if schedule == 'daily':
        return current_time - last_run_time > timedelta(hours=24)
    elif schedule == 'weekly':
        return current_time - last_run_time > timedelta(days=7)
    elif schedule == 'hourly':
        return current_time - last_run_time > timedelta(hours=1)
    
    return False


async def run_orchestrator_cycle():
    """Run one cycle of the orchestrator"""
    logger.info("\n" + "="*60)
    logger.info("🔄 ORCHESTRATOR CYCLE STARTING")
    logger.info("="*60)
    
    # Sort agents by priority
    sorted_agents = sorted(
        AGENTS.items(),
        key=lambda x: {'high': 0, 'medium': 1, 'low': 2}.get(x[1]['priority'], 3)
    )
    
    agents_run = 0
    
    for agent_name, agent_config in sorted_agents:
        if await should_run_agent(agent_name, agent_config['schedule']):
            await run_agent(agent_name, agent_config)
            last_run[agent_name] = datetime.now()
            agents_run += 1
            
            # Small delay between agents
            await asyncio.sleep(2)
    
    if agents_run == 0:
        logger.info("ℹ️ No agents needed to run this cycle")
    else:
        logger.info(f"\n✅ Cycle complete. Ran {agents_run} agents")
    
    return agents_run


async def continuous_orchestrator():
    """Run orchestrator continuously"""
    logger.info("\n🚀 SIMPLE ORCHESTRATOR STARTED")
    logger.info("Press Ctrl+C to stop\n")
    
    cycle_count = 0
    total_runs = 0
    
    try:
        while True:
            cycle_count += 1
            logger.info(f"\n📊 Cycle #{cycle_count}")
            
            agents_run = await run_orchestrator_cycle()
            total_runs += agents_run
            
            # Show stats
            logger.info("\n📈 Orchestrator Stats:")
            logger.info(f"   Cycles: {cycle_count}")
            logger.info(f"   Total agent runs: {total_runs}")
            logger.info(f"   Active agents: {len(AGENTS)}")
            
            # Wait before next cycle (15 minutes)
            logger.info("\n⏳ Waiting 15 minutes before next cycle...")
            await asyncio.sleep(900)
            
    except KeyboardInterrupt:
        logger.info("\n\n🛑 Orchestrator stopped by user")
        logger.info(f"Final stats: {cycle_count} cycles, {total_runs} agent runs")


def run_single_cycle():
    """Run a single orchestrator cycle"""
    asyncio.run(run_orchestrator_cycle())


def show_menu():
    """Display orchestrator menu"""
    print("\n" + "="*60)
    print("🤖 SIMPLE AGENT ORCHESTRATOR")
    print("="*60)
    print("\nConfigured Agents:")
    for name, config in AGENTS.items():
        print(f"  • {name}: {config['description']} ({config['schedule']})")
    
    print("\nOptions:")
    print("1. Run single cycle (run all due agents once)")
    print("2. Run continuously (15-minute cycles)")
    print("3. View agent status")
    print("4. Exit")
    
    return input("\nSelect option (1-4): ")


def view_status():
    """View current agent status"""
    print("\n📊 Agent Status:")
    print("-" * 40)
    
    for name, config in AGENTS.items():
        if name in last_run:
            time_since = datetime.now() - last_run[name]
            print(f"{name}: Last run {time_since} ago")
        else:
            print(f"{name}: Never run")
    
    print("\nDatabase Status:")
    try:
        subprocess.run([sys.executable, "check_current_status.py"], timeout=10)
    except:
        print("Could not fetch database status")


def main():
    """Main entry point"""
    
    while True:
        choice = show_menu()
        
        if choice == '1':
            print("\n🔄 Running single cycle...")
            run_single_cycle()
            print("\n✅ Cycle complete!")
            
        elif choice == '2':
            print("\n🚀 Starting continuous orchestrator...")
            asyncio.run(continuous_orchestrator())
            
        elif choice == '3':
            view_status()
            input("\nPress Enter to continue...")
            
        elif choice == '4':
            print("\n👋 Goodbye!")
            break
            
        else:
            print("\n❌ Invalid choice, please try again")


if __name__ == "__main__":
    main()