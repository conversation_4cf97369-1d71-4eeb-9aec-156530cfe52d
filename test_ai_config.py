#!/usr/bin/env python3
"""Test AI configuration and provider connectivity"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_openai():
    """Test OpenAI connectivity"""
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        return False, "No OpenAI API key found"
    
    try:
        import openai
        openai.api_key = api_key
        
        # Test with a simple completion
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'Hemp database test successful'"}],
            max_tokens=10
        )
        return True, "OpenAI connected successfully"
    except ImportError:
        return False, "OpenAI library not installed. Run: pip install openai"
    except Exception as e:
        return False, f"OpenAI error: {str(e)}"

def test_deepseek():
    """Test DeepSeek connectivity"""
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        return False, "No DeepSeek API key found"
    
    try:
        import requests
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 10
        }
        
        response = requests.post(
            "https://api.deepseek.com/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            return True, "DeepSeek connected successfully"
        else:
            return False, f"DeepSeek API error: {response.status_code}"
    except Exception as e:
        return False, f"DeepSeek error: {str(e)}"

def test_config():
    """Test configuration loading"""
    try:
        from config.agent_config import get_config
        config = get_config()
        
        print("\n🔧 Configuration Status:")
        print(f"  Database URL: {'✅' if config.database.url else '❌'}")
        print(f"  Supabase: {'✅' if config.database.supabase_url else '❌'}")
        print(f"  OpenAI Key: {'✅' if config.ai_providers.openai_api_key else '❌'}")
        print(f"  DeepSeek Key: {'✅' if os.getenv('DEEPSEEK_API_KEY') else '❌'}")
        print(f"  Gemini Key: {'✅' if os.getenv('GEMINI_API_KEY') else '❌'}")
        
        print("\n📊 Agent Settings:")
        print(f"  Log Level: {config.agent.log_level}")
        print(f"  Max Concurrent Tasks: {config.agent.max_concurrent_tasks}")
        print(f"  Rate Limit: {config.agent.rate_limit} req/min")
        
        print("\n🤖 Agents Enabled:")
        print(f"  Research Agent: {'✅' if config.research_agent.enabled else '❌'}")
        print(f"  Plant Part Agent: {'✅' if config.plant_part_agent.enabled else '❌'}")
        
        return True, "Configuration loaded successfully"
    except Exception as e:
        return False, f"Configuration error: {str(e)}"

def main():
    print("🧪 Testing AI Provider Configuration...")
    print("=" * 50)
    
    # Test configuration
    success, message = test_config()
    print(f"\n{'✅' if success else '❌'} Config Test: {message}")
    
    # Test OpenAI
    print("\n🤖 Testing AI Providers...")
    success, message = test_openai()
    print(f"{'✅' if success else '❌'} OpenAI: {message}")
    
    # Test DeepSeek
    success, message = test_deepseek()
    print(f"{'✅' if success else '❌'} DeepSeek: {message}")
    
    print("\n" + "=" * 50)
    print("💡 Next Steps:")
    print("1. If OpenAI fails, check your API key or install: pip install openai")
    print("2. To add Anthropic key, set ANTHROPIC_API_KEY in .env")
    print("3. Run agents with: python run_unified_agent.py")

if __name__ == "__main__":
    main()