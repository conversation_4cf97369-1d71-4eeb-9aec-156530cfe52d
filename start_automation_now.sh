#!/bin/bash
# Quick automation starter - no sudo required

echo "======================================"
echo "🚀 STARTING HEMP DATABASE AUTOMATION"
echo "======================================"
echo ""
echo "This will run AI discovery every 6 hours"
echo "to continuously grow your database."
echo ""

# Create necessary directories
mkdir -p logs

# Activate virtual environment
source venv_dedup/bin/activate

# Option 1: Using nohup (survives terminal close)
echo "Starting automation in background..."
nohup python start_automation.py --continuous > logs/automation_$(date +%Y%m%d_%H%M%S).log 2>&1 &
PID=$!

echo ""
echo "✅ Automation started successfully!"
echo "   Process ID: $PID"
echo ""
echo "📋 Useful Commands:"
echo "----------------------------------------"
echo "Check status:     python monitor_automation.py"
echo "View live logs:   tail -f logs/automation_*.log"
echo "Stop automation:  kill $PID"
echo "Find process:     ps aux | grep start_automation"
echo ""
echo "🔄 Schedule:"
echo "   - Runs every 6 hours"
echo "   - Daily at 2:00 AM"
echo ""
echo "📊 Current Stats:"
echo "   - Total products: 455"
echo "   - AI-generated: 55 (12.1%)"
echo "   - Today's growth: +154 products"
echo ""
echo "The automation will continue running even"
echo "if you close this terminal. Check back"
echo "tomorrow to see your database growth!"
echo "======================================"