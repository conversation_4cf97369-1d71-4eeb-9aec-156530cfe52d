#!/usr/bin/env python3
"""
Simple automation starter for Hemp Database
Easy way to enable daily agent runs
"""

import os
import sys
import time
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import schedule
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('automation.log')
    ]
)
logger = logging.getLogger(__name__)


def run_ai_discovery():
    """Run AI-powered product discovery"""
    logger.info("🤖 Running AI Discovery Agent...")
    
    try:
        # Add --auto flag to skip interactive prompts
        result = subprocess.run(
            [sys.executable, "launch_enhanced_ai_agent.py"],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        # Extract key metrics from output
        output_lines = result.stdout.strip().split('\n')
        for line in output_lines[-20:]:  # Check last 20 lines
            if 'Total products:' in line or 'Products added:' in line or 'FINAL STATS' in line:
                logger.info(f"  {line.strip()}")
                
        logger.info("✅ AI Discovery completed")
        return True
        
    except subprocess.TimeoutExpired:
        logger.error("❌ AI Discovery timed out")
        return False
    except Exception as e:
        logger.error(f"❌ AI Discovery error: {e}")
        return False


def run_deduplication():
    """Run deduplication to merge duplicate products"""
    logger.info("🧹 Running Deduplication...")
    
    try:
        if Path("merge_exact_duplicates.py").exists():
            result = subprocess.run(
                [sys.executable, "merge_exact_duplicates.py"],
                capture_output=True,
                text=True,
                timeout=120
            )
            logger.info("✅ Deduplication completed")
            return True
        else:
            logger.info("⏭️ Deduplication script not found, skipping")
            return True
            
    except Exception as e:
        logger.error(f"❌ Deduplication error: {e}")
        return False


def run_dashboard():
    """Generate daily dashboard report"""
    logger.info("📊 Generating Dashboard...")
    
    try:
        result = subprocess.run(
            [sys.executable, "simple_agent_dashboard.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        # Show key stats
        if result.returncode == 0:
            logger.info("✅ Dashboard generated")
            
            # Try to read and show summary
            if Path("agent_dashboard.json").exists():
                import json
                with open("agent_dashboard.json", 'r') as f:
                    data = json.load(f)
                    logger.info(f"  Total products: {data.get('total_products', 'Unknown')}")
                    
        return True
        
    except Exception as e:
        logger.error(f"❌ Dashboard error: {e}")
        return False


def daily_automation_cycle():
    """Run complete daily automation cycle"""
    logger.info("\n" + "="*60)
    logger.info("🔄 DAILY AUTOMATION CYCLE STARTING")
    logger.info("="*60)
    
    start_time = datetime.now()
    
    # Run each component
    steps = [
        ("AI Discovery", run_ai_discovery),
        ("Deduplication", run_deduplication),
        ("Dashboard", run_dashboard)
    ]
    
    successful = 0
    for step_name, step_func in steps:
        logger.info(f"\n▶️  Step: {step_name}")
        if step_func():
            successful += 1
        time.sleep(5)  # Small pause between steps
    
    # Summary
    duration = datetime.now() - start_time
    logger.info(f"\n✅ Cycle complete: {successful}/{len(steps)} steps successful")
    logger.info(f"⏱️  Duration: {duration.total_seconds():.1f} seconds")
    logger.info("="*60)


def setup_schedule():
    """Setup daily schedule"""
    # Schedule daily runs at 2 AM
    schedule.every().day.at("02:00").do(daily_automation_cycle)
    
    # Also run every 6 hours for testing
    schedule.every(6).hours.do(daily_automation_cycle)
    
    logger.info("📅 Schedule configured:")
    logger.info("  - Daily at 2:00 AM")
    logger.info("  - Every 6 hours")


def main():
    """Main automation runner"""
    print("\n" + "="*60)
    print("🤖 HEMP DATABASE AUTOMATION")
    print("="*60)
    print("\nOptions:")
    print("1. Run automation once now")
    print("2. Start continuous automation (runs daily)")
    print("3. Test individual components")
    print("4. Show setup instructions")
    
    choice = input("\nSelect option (1-4): ")
    
    if choice == '1':
        print("\n🚀 Running automation cycle...")
        daily_automation_cycle()
        
    elif choice == '2':
        print("\n🚀 Starting continuous automation...")
        print("Press Ctrl+C to stop\n")
        
        # Run once immediately
        daily_automation_cycle()
        
        # Setup schedule
        setup_schedule()
        
        # Keep running
        logger.info("\n⏰ Automation scheduled. Waiting for next run...")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("\n🛑 Automation stopped by user")
            
    elif choice == '3':
        print("\nTest which component?")
        print("1. AI Discovery")
        print("2. Deduplication")
        print("3. Dashboard")
        
        test_choice = input("\nSelect (1-3): ")
        
        if test_choice == '1':
            run_ai_discovery()
        elif test_choice == '2':
            run_deduplication()
        elif test_choice == '3':
            run_dashboard()
            
    elif choice == '4':
        print("\n📋 SETUP INSTRUCTIONS")
        print("="*40)
        print("\n1. EASIEST: Run in background with nohup")
        print("   nohup python start_automation.py 2 > automation.out &")
        print("\n2. RECOMMENDED: Use screen")
        print("   screen -S hemp-auto")
        print("   python start_automation.py")
        print("   # Press Ctrl+A then D to detach")
        print("\n3. VIEW LOGS:")
        print("   tail -f automation.log")
        print("\n4. STOP AUTOMATION:")
        print("   ps aux | grep start_automation.py")
        print("   kill <PID>")


if __name__ == "__main__":
    # Check if running with command line argument
    if len(sys.argv) > 1:
        if sys.argv[1] == '--run':
            daily_automation_cycle()
        elif sys.argv[1] == '--continuous':
            logger.info("Starting continuous automation...")
            daily_automation_cycle()
            setup_schedule()
            while True:
                schedule.run_pending()
                time.sleep(60)
    else:
        main()