#!/usr/bin/env python3
"""
Enhanced AI-Powered Hemp Product Discovery Agent
Better parsing and more reliable product generation
"""

import asyncio
import logging
import json
import re
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_providers.multi_provider import get_ai_client
from dotenv import load_dotenv

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedAIDiscoveryAgent:
    """Enhanced agent with better parsing and product extraction"""
    
    def __init__(self, conn):
        self.conn = conn
        self.ai_client = get_ai_client()
        self.products_added = 0
        
    async def get_plant_part_id(self, name: str) -> int:
        """Get plant part ID by name"""
        result = await self.conn.fetchval(
            "SELECT id FROM plant_parts WHERE name = $1",
            name
        )
        return result
        
    async def get_industry_ids(self) -> dict:
        """Get all industry subcategory IDs"""
        rows = await self.conn.fetch(
            "SELECT id, name FROM industry_sub_categories"
        )
        return {row['name']: row['id'] for row in rows}
        
    async def check_duplicate(self, name: str, plant_part_id: int) -> bool:
        """Check if product already exists"""
        count = await self.conn.fetchval(
            """
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name) = LOWER($1) AND plant_part_id = $2
            """,
            name, plant_part_id
        )
        return count > 0
        
    async def insert_product(self, product_data: dict) -> int:
        """Insert a new product"""
        query = """
            INSERT INTO uses_products (
                name, description, plant_part_id, industry_sub_category_id,
                benefits_advantages, keywords, source_url, source_type,
                source_agent, confidence_score, verification_status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """
        
        product_id = await self.conn.fetchval(
            query,
            product_data['name'],
            product_data.get('description', ''),
            product_data['plant_part_id'],
            product_data.get('industry_sub_category_id'),
            product_data.get('benefits_advantages', []),
            product_data.get('keywords', []),
            product_data.get('source_url', ''),
            'ai_agent',
            'ai_discovery_agent',
            product_data.get('confidence_score', 0.85),
            'ai_verified'
        )
        
        return product_id
    
    def parse_ai_response(self, response_text: str, plant_part: str) -> list:
        """Parse AI response to extract products"""
        products = []
        
        # Try JSON parsing first
        try:
            parsed = json.loads(response_text)
            if isinstance(parsed, list):
                return parsed
        except:
            pass
        
        # Parse numbered list format
        # Look for patterns like "1. Product Name" or "**Product Name**"
        lines = response_text.split('\n')
        current_product = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check for numbered items (1., 2., etc.)
            num_match = re.match(r'^(\d+)\.\s*\*?\*?([^*]+)\*?\*?', line)
            if num_match:
                # Save previous product if exists
                if current_product and 'name' in current_product:
                    products.append(current_product)
                
                # Start new product
                current_product = {
                    'name': num_match.group(2).strip(),
                    'benefits': [],
                    'description': ''
                }
                continue
            
            # Look for description after name
            if current_product and 'name' in current_product:
                # Check for description patterns
                if 'Description:' in line or '-' in line[:3]:
                    desc_text = line.split(':', 1)[-1].strip()
                    desc_text = desc_text.lstrip('-').strip()
                    current_product['description'] = desc_text
                elif 'Key Innovation:' in line or 'Innovation:' in line:
                    innovation = line.split(':', 1)[-1].strip()
                    current_product['benefits'].append(f"Innovation: {innovation}")
                elif not line.startswith(('*', '-', '•')) and len(line) > 20:
                    # Likely continuation of description
                    if current_product['description']:
                        current_product['description'] += ' ' + line
                    else:
                        current_product['description'] = line
        
        # Don't forget the last product
        if current_product and 'name' in current_product:
            products.append(current_product)
        
        # Ensure each product has required fields
        for product in products:
            if 'description' not in product:
                product['description'] = f"Innovative {plant_part.lower()} product"
            if 'industry' not in product:
                product['industry'] = self._guess_industry(product['name'], plant_part)
            if 'benefits' not in product or not product['benefits']:
                product['benefits'] = [f"Made from {plant_part}", "Sustainable", "Innovative"]
        
        return products
    
    def _guess_industry(self, product_name: str, plant_part: str) -> str:
        """Guess industry based on product name and plant part"""
        name_lower = product_name.lower()
        
        # Industry mapping
        if any(word in name_lower for word in ['medicine', 'pharmaceutical', 'drug', 'therapy']):
            return 'Pharmaceuticals'
        elif any(word in name_lower for word in ['supplement', 'vitamin', 'nutrient']):
            return 'Nutraceuticals'
        elif any(word in name_lower for word in ['cosmetic', 'cream', 'serum', 'balm', 'lotion']):
            return 'Cosmetics & Personal Care'
        elif any(word in name_lower for word in ['textile', 'fabric', 'cloth', 'fiber']):
            return 'Textiles'
        elif any(word in name_lower for word in ['food', 'beverage', 'drink', 'edible']):
            return 'Food & Beverage'
        elif any(word in name_lower for word in ['plastic', 'polymer', 'composite']):
            return 'Biocomposites'
        elif any(word in name_lower for word in ['construction', 'building', 'insulation']):
            return 'Construction Materials'
        else:
            return 'Other'
    
    async def discover_products_enhanced(self, plant_part: str, count: int = 5):
        """Enhanced discovery with better prompting"""
        
        logger.info(f"🤖 Enhanced AI Discovery for {plant_part}")
        
        # Get plant part ID
        plant_part_id = await self.get_plant_part_id(plant_part)
        if not plant_part_id:
            logger.error(f"Plant part '{plant_part}' not found!")
            return
            
        # Get industry IDs
        industry_ids = await self.get_industry_ids()
        
        # Use a simpler, more reliable prompt
        prompt = f"""Generate {count} innovative hemp {plant_part} products. 
For each product, provide:

1. **Product Name Here**
   - Description: A clear 1-2 sentence description of what this product is and how it works
   - Key Innovation: What makes this product special or innovative
   
2. **Another Product Name**
   - Description: Clear description of the product
   - Key Innovation: The innovative aspect

Continue this pattern for all {count} products. Focus on real, feasible innovations."""

        system_prompt = "You are a hemp industry expert. Generate realistic, innovative hemp products with clear names and descriptions."
        
        # Try with preferred provider first (Gemini tends to be more creative)
        response = self.ai_client.generate(prompt, system_prompt, temperature=0.8, preferred_provider="gemini")
        
        if not response.success:
            logger.warning(f"Gemini failed, trying DeepSeek: {response.error}")
            response = self.ai_client.generate(prompt, system_prompt, temperature=0.7, preferred_provider="deepseek")
        
        if not response.success:
            logger.error(f"All providers failed: {response.error}")
            return
            
        logger.info(f"Got response from {response.provider}")
        
        # Parse products
        products = self.parse_ai_response(response.content, plant_part)
        logger.info(f"Parsed {len(products)} products from AI response")
        
        # Add products to database
        for product in products:
            try:
                # Check for duplicate
                if await self.check_duplicate(product.get('name', ''), plant_part_id):
                    logger.info(f"Skipping duplicate: {product.get('name')}")
                    continue
                
                # Find matching industry
                industry_name = product.get('industry', 'Other')
                industry_id = industry_ids.get(industry_name)
                
                if not industry_id:
                    # Try to find partial match
                    for ind_name, ind_id in industry_ids.items():
                        if industry_name.lower() in ind_name.lower():
                            industry_id = ind_id
                            break
                
                # Prepare product data
                product_data = {
                    'name': product.get('name', f'{plant_part} Innovation #{self.products_added + 1}'),
                    'description': product.get('description', ''),
                    'plant_part_id': plant_part_id,
                    'industry_sub_category_id': industry_id,
                    'benefits_advantages': product.get('benefits', []),
                    'keywords': [plant_part.lower(), 'innovative', 'ai-discovered', '2025'],
                    'source_url': f"AI Generated ({response.provider})",
                    'confidence_score': 0.85
                }
                
                # Insert product
                product_id = await self.insert_product(product_data)
                logger.info(f"✅ Added: {product_data['name']} (ID: {product_id})")
                self.products_added += 1
                
                await asyncio.sleep(0.3)  # Small delay
                
            except Exception as e:
                logger.error(f"Error adding product: {e}")
                continue


async def run_enhanced_discovery():
    """Run enhanced discovery session"""
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        logger.error("DATABASE_URL not found")
        return
        
    parsed = urlparse(database_url)
    
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    
    agent = EnhancedAIDiscoveryAgent(conn)
    
    # Focus on areas that need products
    targets = [
        ("Terpenes", 5),      # Priority - only has 12 products
        ("Hemp Roots", 4),    # Has 20 products
        ("Cannabinoids", 4),  # Has 32 products
        ("Hemp Leaves", 3),   # Has 25 products
    ]
    
    print("\n" + "="*60)
    print("🚀 ENHANCED AI DISCOVERY SESSION")
    print("="*60)
    
    for plant_part, count in targets:
        print(f"\n📌 Discovering {count} {plant_part} products...")
        await agent.discover_products_enhanced(plant_part, count)
        print(f"   Added {agent.products_added} products")
        agent.products_added = 0  # Reset
        await asyncio.sleep(2)
    
    # Final stats
    total_count = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
    ai_count = await conn.fetchval(
        "SELECT COUNT(*) FROM uses_products WHERE source_agent = 'ai_discovery_agent'"
    )
    
    print("\n" + "="*60)
    print("📊 FINAL STATS")
    print("="*60)
    print(f"Total products: {total_count}")
    print(f"AI-generated products: {ai_count}")
    print(f"AI percentage: {ai_count/total_count*100:.1f}%")
    
    await conn.close()


if __name__ == "__main__":
    asyncio.run(run_enhanced_discovery())