#!/usr/bin/env python3
"""
AI-Powered Hemp Product Discovery Agent
Uses DeepSeek and Gemini for intelligent product discovery
"""

import asyncio
import logging
import json
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_providers.multi_provider import get_ai_client, AIResponse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AIProductDiscoveryAgent:
    """Agent that uses AI to discover innovative hemp products"""
    
    def __init__(self, conn):
        self.conn = conn
        self.ai_client = get_ai_client()
        self.products_added = 0
        
    async def get_plant_part_id(self, name: str) -> int:
        """Get plant part ID by name"""
        result = await self.conn.fetchval(
            "SELECT id FROM plant_parts WHERE name = $1",
            name
        )
        return result
        
    async def get_industry_ids(self) -> dict:
        """Get all industry subcategory IDs"""
        rows = await self.conn.fetch(
            "SELECT id, name FROM industry_sub_categories"
        )
        return {row['name']: row['id'] for row in rows}
        
    async def check_duplicate(self, name: str, plant_part_id: int) -> bool:
        """Check if product already exists"""
        count = await self.conn.fetchval(
            """
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name) = LOWER($1) AND plant_part_id = $2
            """,
            name, plant_part_id
        )
        return count > 0
        
    async def insert_product(self, product_data: dict) -> int:
        """Insert a new product"""
        query = """
            INSERT INTO uses_products (
                name, description, plant_part_id, industry_sub_category_id,
                benefits_advantages, keywords, source_url, source_type,
                source_agent, confidence_score, verification_status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """
        
        product_id = await self.conn.fetchval(
            query,
            product_data['name'],
            product_data.get('description', ''),
            product_data['plant_part_id'],
            product_data.get('industry_sub_category_id'),
            product_data.get('benefits_advantages', []),
            product_data.get('keywords', []),
            product_data.get('source_url', ''),
            product_data.get('source_type', 'ai_agent'),
            'ai_discovery_agent',
            product_data.get('confidence_score', 0.85),
            'ai_verified'
        )
        
        return product_id
    
    async def discover_products(self, plant_part: str, count: int = 10, innovative: bool = True):
        """Discover products for a specific plant part using AI"""
        
        logger.info(f"🤖 AI Discovery Agent starting for {plant_part}")
        logger.info(f"Using providers: {self.ai_client.get_available_providers()}")
        
        # Get plant part ID
        plant_part_id = await self.get_plant_part_id(plant_part)
        if not plant_part_id:
            logger.error(f"Plant part '{plant_part}' not found!")
            return
            
        # Get industry IDs
        industry_ids = await self.get_industry_ids()
        
        # Generate products using AI
        logger.info(f"Generating {count} {'innovative' if innovative else 'commercial'} products...")
        
        products = self.ai_client.generate_hemp_products(
            plant_part=plant_part,
            count=count,
            innovative=innovative
        )
        
        if not products:
            logger.error("No products generated by AI")
            return
            
        logger.info(f"AI generated {len(products)} product ideas")
        
        # Process each product
        for i, product in enumerate(products, 1):
            try:
                # Check for duplicate
                if await self.check_duplicate(product.get('name', ''), plant_part_id):
                    logger.info(f"Skipping duplicate: {product.get('name')}")
                    continue
                
                # Map industry
                industry_name = product.get('industry', 'Other')
                industry_id = None
                
                # Try exact match first
                if industry_name in industry_ids:
                    industry_id = industry_ids[industry_name]
                else:
                    # Try partial match
                    for ind_name, ind_id in industry_ids.items():
                        if industry_name.lower() in ind_name.lower() or ind_name.lower() in industry_name.lower():
                            industry_id = ind_id
                            break
                
                # Prepare product data
                product_data = {
                    'name': product.get('name', f'AI Product {i}'),
                    'description': product.get('description', ''),
                    'plant_part_id': plant_part_id,
                    'industry_sub_category_id': industry_id,
                    'benefits_advantages': product.get('benefits', []),
                    'keywords': [plant_part.lower(), 'ai-discovered', 'innovative'],
                    'source_url': f"AI: {self.ai_client.providers[0].__class__.__name__}",
                    'source_type': 'ai_agent',
                    'confidence_score': 0.85 if innovative else 0.90
                }
                
                # Add technical specs to description if available
                if 'technical_specs' in product:
                    product_data['description'] += f" Technical specs: {product['technical_specs']}"
                
                # Insert product
                product_id = await self.insert_product(product_data)
                logger.info(f"✅ Added: {product_data['name']} (ID: {product_id})")
                self.products_added += 1
                
                # Small delay to avoid overwhelming the database
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error processing product: {e}")
                continue
        
        logger.info(f"\n{'='*60}")
        logger.info(f"AI DISCOVERY COMPLETE")
        logger.info(f"{'='*60}")
        logger.info(f"Products added: {self.products_added}")
        logger.info(f"Plant part: {plant_part}")
        logger.info(f"AI providers used: {self.ai_client.get_available_providers()}")


async def run_discovery_session():
    """Run a discovery session for multiple plant parts"""
    
    # Database connection
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        logger.error("DATABASE_URL not found in environment")
        return
        
    parsed = urlparse(database_url)
    
    # Connect to database
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    
    # Create agent
    agent = AIProductDiscoveryAgent(conn)
    
    # Discovery targets (prioritize areas with fewer products)
    targets = [
        ("Terpenes", 8, True),          # Only 10 products, needs growth
        ("Cannabinoids", 8, True),      # 31 products, could use more
        ("Hemp Roots", 6, True),        # 19 products, needs variety
        ("Hemp Leaves", 6, True),       # 24 products, needs variety
        ("Hemp Flowers", 5, False),     # 33 products, add commercial
        ("Hemp Hurd (Shivs)", 5, True), # 51 products, add innovative
    ]
    
    total_products = 0
    
    for plant_part, count, innovative in targets:
        logger.info(f"\n{'='*60}")
        logger.info(f"Discovering {plant_part} products...")
        
        await agent.discover_products(plant_part, count, innovative)
        total_products += agent.products_added
        agent.products_added = 0  # Reset counter
        
        # Delay between plant parts
        await asyncio.sleep(2)
    
    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info(f"DISCOVERY SESSION COMPLETE")
    logger.info(f"{'='*60}")
    logger.info(f"Total products added: {total_products}")
    logger.info(f"AI providers: {agent.ai_client.get_available_providers()}")
    
    # Show final database stats
    total_count = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
    ai_verified = await conn.fetchval(
        "SELECT COUNT(*) FROM uses_products WHERE verification_status = 'ai_verified'"
    )
    
    logger.info(f"\nDatabase Status:")
    logger.info(f"Total products: {total_count}")
    logger.info(f"AI verified: {ai_verified} ({ai_verified/total_count*100:.1f}%)")
    
    await conn.close()


async def test_single_discovery():
    """Test discovering a few products for one plant part"""
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        logger.error("DATABASE_URL not found")
        return
        
    parsed = urlparse(database_url)
    
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    agent = AIProductDiscoveryAgent(conn)
    
    # Test with Terpenes (needs more products)
    await agent.discover_products("Terpenes", count=3, innovative=True)
    
    await conn.close()


def main():
    """Main entry point"""
    print("\n" + "="*60)
    print("🤖 AI-POWERED HEMP PRODUCT DISCOVERY")
    print("="*60)
    
    ai_client = get_ai_client()
    print(f"\nAvailable AI providers: {ai_client.get_available_providers()}")
    
    print("\nOptions:")
    print("1. Run full discovery session (add ~40 products)")
    print("2. Test with single plant part (add 3 products)")
    print("3. Exit")
    
    choice = input("\nSelect option (1-3): ")
    
    if choice == '1':
        print("\n🚀 Starting full discovery session...")
        print("This will add products for multiple plant parts using AI")
        asyncio.run(run_discovery_session())
    elif choice == '2':
        print("\n🧪 Running test discovery...")
        asyncio.run(test_single_discovery())
    else:
        print("\nGoodbye!")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--auto':
        # Auto mode for orchestrator
        asyncio.run(run_discovery_session())
    else:
        # Interactive mode
        main()