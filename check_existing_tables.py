#!/usr/bin/env python3
"""Check existing agent tables in the database."""
import os
import psycopg2
from urllib.parse import urlparse, unquote

def check_existing_tables():
    """Check what agent tables already exist."""
    # Get database URL
    database_url = os.environ.get('DATABASE_URL', 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require')
    
    # Parse the URL
    parsed = urlparse(database_url)
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            database=parsed.path[1:],
            user=parsed.username,
            password=unquote(parsed.password),
            sslmode='require'
        )
        
        with conn.cursor() as cur:
            print("🔍 Checking existing agent-related tables...")
            print("="*60)
            
            # Check for any agent-related tables
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND (table_name LIKE 'agent_%' OR table_name LIKE '%agent%')
                ORDER BY table_name
            """)
            tables = cur.fetchall()
            
            if tables:
                print(f"\n📊 Found {len(tables)} agent-related tables:")
                for (table_name,) in tables:
                    print(f"\n📋 Table: {table_name}")
                    
                    # Get columns
                    cur.execute("""
                        SELECT column_name, data_type, is_nullable
                        FROM information_schema.columns
                        WHERE table_schema = 'public' AND table_name = %s
                        ORDER BY ordinal_position
                    """, (table_name,))
                    
                    columns = cur.fetchall()
                    print("   Columns:")
                    for col_name, data_type, nullable in columns:
                        null_str = "NULL" if nullable == 'YES' else "NOT NULL"
                        print(f"     - {col_name}: {data_type} {null_str}")
                    
                    # Get row count
                    cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cur.fetchone()[0]
                    print(f"   Row count: {count}")
            else:
                print("No agent-related tables found.")
            
            # Check for functions
            print("\n🔧 Checking for agent-related functions:")
            cur.execute("""
                SELECT routine_name 
                FROM information_schema.routines 
                WHERE routine_schema = 'public' 
                AND routine_name LIKE '%agent%'
            """)
            functions = cur.fetchall()
            
            if functions:
                for (func_name,) in functions:
                    print(f"   - {func_name}")
            else:
                print("   No agent-related functions found.")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking tables: {e}")

if __name__ == "__main__":
    check_existing_tables()