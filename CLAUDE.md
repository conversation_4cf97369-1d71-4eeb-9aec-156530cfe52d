# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Current Status (July 3, 2025)

### 🚀 Multi-Provider AI & Hourly Automation Active!
- **Products**: 486+ total (growing ~15-20/hour), 17.7% AI-verified
- **AI Providers**: DeepSeek + Gemini working in tandem
- **Automation**: Running hourly (PID 88054)
- **Growth Rate**: ~400 products/day
- **Companies**: 136 companies, 20 with logos
- **Research**: 19 papers, 15 with images

### 🤖 Key Systems Implemented
1. **Multi-Provider AI**: DeepSeek (primary) + Gemini (fallback) + OpenAI (quota exceeded)
2. **Hourly Automation**: Runs every hour adding 15-20 products
3. **Enhanced AI Agents**: Better parsing, innovative products, technical details
4. **Cloud Ready**: Oracle Cloud deployment guide + database export
5. **Unified CLI** (`./hemp`): Single entry point for all operations

### 🎯 Latest Updates (July 3, 2025)
- **Multi-Provider System**: `ai_providers/multi_provider.py` handles 3 AI providers
- **Hourly Schedule**: Changed from 6-hour to 1-hour cycles
- **Database Growth**: 400 → 486+ products (21.5% increase today)
- **AI Quality**: Products now have detailed descriptions and innovations
- **Export Ready**: Database exported for cloud migration
- **Image Analysis**: 202/486 products have images (needs enhancement)
- **Current Status**: 486+ products, 17.7% AI-verified, hourly automation active

## Quick Start Commands

```bash
# Development
cd HempResourceHub
npm install
npm run dev                    # Starts Express + Vite

# Database
npm run db:push               # Push schema changes
psql $DATABASE_URL -f migrations/add_deduplication_fields.sql  # Add dedup fields

# Multi-Provider AI & Automation
source venv_dedup/bin/activate
./RUN_AUTOMATION.sh                        # Start hourly automation (easiest)
python monitor_automation.py               # Check automation status
python simple_agent_dashboard.py           # Agent performance dashboard
tail -f logs/automation_hourly_*.log       # View live automation logs

# AI-Powered Agents
python launch_enhanced_ai_agent.py         # Enhanced multi-provider agent
python demo_ai_providers.py                # Test AI providers
python check_product_images.py             # Check image coverage

# Cloud Deployment
python export_database.py                  # Export for cloud migration
cat CLOUD_DEPLOYMENT_GUIDE.md              # Cloud setup instructions

# Deduplication
python scripts/simple_deduplication.py     # Find duplicates
python merge_exact_duplicates.py           # Merge duplicates

# Image Scraping
python run_simple_scrapers.py              # Without attribution
python run_image_scrapers_fixed.py         # With attribution

# Unified CLI
./hemp agent research "query" --features company image
./hemp images generate --provider stable-diffusion
./hemp monitor --live
./hemp db export --format json

# Enhanced Scrapers
python enhanced_research_scraper.py        # PubMed integration
python enhanced_company_scraper.py         # Logo extraction
```

## Project Architecture

### Tech Stack
- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Express.js + Drizzle ORM
- **Database**: PostgreSQL via Supabase
- **UI**: shadcn/ui components + custom animations
- **State**: React Query (TanStack Query)

### Database Schema
```
hemp_plant_archetypes → plant_parts → uses_products
                                    ↘ industry_sub_categories
                                    ↘ hemp_companies
                                    ↘ research_entries
```

### Key Tables (Actual Names)
- `hemp_plant_archetypes` (NOT plant_types)
- `uses_products` (NOT hemp_products)
- `industry_sub_categories` (NOT sub_industries)
- `research_entries` (NOT research_papers)
- `plant_parts` with `plant_type_id` FK

## Recent Features & Fixes

### UI/UX Enhancements (Augment Code)
- **Navigation**: Simplified to single `/products` route
- **Admin Panel**: 9→5 tabs with dropdown selectors
- **Visual**: Modern cards, gradients, hemp growing loader
- **Performance**: 53% navigation complexity reduction
- **Animations**: 4 new components (hemp-growing-loader, etc.)

### Data & Backend (Claude)
- **Product Discovery**: Manual + Python scripts
- **Company System**: Extraction, relationships, deduplication
- **Image Generation**: Integrated with research agent
- **Attribution**: Legal compliance for scraped content

### Fixed Issues
1. ✅ Database table name mismatches
2. ✅ Image display with proper fallbacks
3. ✅ Duplicate image generation loop
4. ✅ Research frontend column mapping
5. ✅ SSL/authentication for development
6. ✅ Content Security Policy for fonts
7. ✅ All npm vulnerabilities (prismjs, esbuild)
8. ✅ GitHub Actions updated to latest versions
9. ✅ SSL certificate issues for Drizzle database connections
10. ✅ Claude API credit errors (service temporarily disabled)
11. ✅ TypeScript component errors (25+ fixed)
12. ✅ Supabase client API usage (.table → .from)
13. ✅ Property name mappings (industryId, benefits_advantages, etc.)
14. ✅ Missing module implementations
15. ✅ Deduplication system (found 66 duplicates)
16. ✅ Plant-part agent architecture designed
17. ✅ Database migration for versioning ready

## Environment Variables

```bash
# Required
VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co
VITE_SUPABASE_ANON_KEY=[from Supabase dashboard]
DATABASE_URL=postgresql://postgres:[password]@db.ktoqznqmlnxrtvubewyz.supabase.co:5432/postgres

# Optional
SUPABASE_SERVICE_ROLE_KEY=[for admin operations]
OPENAI_API_KEY=[for AI features]
```

## Development Workflow

1. **Frontend Changes**: Edit components in `/client/src/`, hot-reload via Vite
2. **Database Changes**: Update `/shared/schema.ts`, run `npm run db:push`
3. **API Changes**: Update `/server/routes.ts` and `/server/storage-db.ts`
4. **Python Scripts**: Use for data population and scraping
5. **Testing**: `npm run test` for API tests

## Key Components & Files

### Frontend
- `/client/src/components/ui/` - Reusable UI components
- `/client/src/components/animations/` - Hemp-themed animations
- `/client/src/pages/` - Route page components
- `/client/src/hooks/` - React Query data hooks

### Backend
- `/server/index.ts` - Express server setup
- `/server/routes.ts` - API endpoints
- `/server/storage-db.ts` - Database queries
- `/shared/schema.ts` - Shared type definitions

### Python Scripts
- `enhanced_research_scraper.py` - PubMed/article scraping
- `enhanced_company_scraper.py` - Logo extraction
- `run_simple_scrapers.py` - Pipeline runner
- `quick_add_product.py` - Manual product entry

### UI Components Created
- `attributed-image.tsx` - Image with source attribution
- `hemp-growing-loader.tsx` - Plant growth animation
- `smart-search.tsx` - AI-powered search
- `interactive-product-card.tsx` - Enhanced cards
- `data-visualization-dashboard.tsx` - Analytics
- `alphabet-filter.tsx` - A-Z filtering component

## Common Tasks

### Adding Products
```python
# Manual with companies
python quick_add_product.py

# Bulk import from CSV
python bulk_import_products.py

# Via research agent
python run_agent_with_images.py
```

### Running Scrapers
```python
# Company logos
python simple_logo_scraper.py

# Research images
python hemp_industry_daily_scraper.py

# Full pipeline
python run_simple_scrapers.py
```

### Troubleshooting

#### Puppeteer in WSL
```bash
sudo apt-get update
sudo apt-get install -y chromium-browser
sudo apt-get install -y libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0
```

#### Database Connection
- Check URL encoding for special characters in password
- Use `sslmode=disable` for local development
- Ensure all environment variables are set

#### Image Issues
- Verify `/client/public/images/` directory exists
- Check for proper `?url` suffix in Vite imports
- Use fallback images for missing content

## Important Notes

- Always use `plant_type_id` not `archetype_id` (DB uses the former)
- Products table is `uses_products` not `hemp_products`
- Research table is `research_entries` not `research_papers`
- Use SERVICE_ROLE_KEY for admin operations (bypasses RLS)
- Keep git commits under 50MB (no node_modules)

### AI Service Status (July 1, 2025)
- **Claude API**: Temporarily disabled due to insufficient credits
- **To Re-enable**:
  1. Add credits to Anthropic account
  2. Uncomment line 12 in `/server/routes.ts`
  3. Uncomment line 400 in `/server/routes.ts`
- **SSL Fix for Drizzle**: Use `NODE_TLS_REJECT_UNAUTHORIZED=0 npm run db:push`

### Known Issues (July 2, 2025)
- **TypeScript Errors**: ~16 remaining errors (mostly server-side) - down from 229
- **Missing npm lint script**: Add ESLint configuration
- **SSL Certificate Warning**: Requires NODE_TLS_REJECT_UNAUTHORIZED=0 for DB operations
- **Fixed Issues**: ✅ All component TypeScript errors resolved

## Support & Feedback

- **Help**: Use `/help` command
- **Issues**: Report at https://github.com/anthropics/claude-code/issues
- **Docs**: https://docs.anthropic.com/en/docs/claude-code