# Advanced Agent System Requirements

# Core Dependencies
python-dotenv==1.0.0
asyncio==3.4.3
aiohttp==3.12.13
asyncpg==0.29.0
psycopg2-binary==2.9.10

# AI/ML Frameworks
langchain==0.3.26
langchain-community==0.3.27
langchain-openai==0.1.25
langchain-anthropic==0.1.11
openai==1.35.0
anthropic==0.28.0
transformers==4.40.0
sentence-transformers==2.7.0
tiktoken==0.7.0

# NLP & Text Processing
spacy==3.7.4
nltk==3.8.1
beautifulsoup4==4.13.4
newspaper3k==0.2.8
trafilatura==1.9.0
markdownify==0.12.1

# Vector Databases & Embeddings
chromadb==0.5.0
faiss-cpu==1.8.0
pinecone-client==3.2.2
qdrant-client==1.9.0

# Web Scraping & APIs
scrapy==2.11.0
selenium==4.20.0
playwright==1.44.0
requests==2.32.4
httpx==0.28.1
feedparser==6.0.11

# Data Processing
pandas==2.2.2
numpy==2.3.1
rapidfuzz==3.9.3
jsonschema==4.22.0
pydantic==2.11.7
marshmallow==3.26.1

# Task Queue & Scheduling
celery==5.4.0
redis==5.0.4
apscheduler==3.10.4
dramatiq==1.16.0

# Monitoring & Logging
prometheus-client==0.20.0
sentry-sdk==2.5.0
structlog==24.2.0
rich==13.7.1

# Testing & Quality
pytest==8.2.2
pytest-asyncio==0.23.7
pytest-cov==5.0.0
black==24.4.2
ruff==0.4.8
mypy==1.10.0

# API & Web Framework (if needed)
fastapi==0.111.0
uvicorn==0.30.1
pydantic-settings==2.10.1

# Database Tools
alembic==1.13.1
sqlalchemy==2.0.41

# Search & Analytics
elasticsearch==8.13.2
whoosh==2.7.4

# Image Processing (for vision agents)
pillow==10.3.0
opencv-python==********

# Performance & Caching
cachetools==5.3.3
diskcache==5.6.3
joblib==1.4.2

# Security
cryptography==42.0.8
pyjwt==2.8.0

# Development Tools
ipython==8.24.0
jupyter==1.0.0
python-multipart==0.0.9

# Optional Advanced Features
# tensorflow==2.16.1  # For advanced ML
# torch==2.3.1  # For PyTorch models
# huggingface-hub==0.23.4  # For HF models
# wandb==0.17.1  # For ML experiment tracking