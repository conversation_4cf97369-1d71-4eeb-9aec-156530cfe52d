#!/usr/bin/env python3
"""
Launch the Hemp Leaves discovery agent
This agent will search for hemp leaf products and populate the database
"""

import asyncio
import logging
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleSupabaseClient:
    """Minimal Supabase client for agent use"""
    
    def __init__(self, conn):
        self.conn = conn
        
    async def get_plant_part_id(self, name: str) -> int:
        """Get plant part ID by name"""
        result = await self.conn.fetchval(
            "SELECT id FROM plant_parts WHERE name = $1",
            name
        )
        return result
        
    async def get_industry_ids(self) -> dict:
        """Get all industry subcategory IDs"""
        rows = await self.conn.fetch(
            "SELECT id, name FROM industry_sub_categories"
        )
        return {row['name']: row['id'] for row in rows}
        
    async def insert_product(self, product_data: dict) -> int:
        """Insert a new product"""
        query = """
            INSERT INTO uses_products (
                name, description, plant_part_id, industry_sub_category_id,
                benefits_advantages, keywords, source_url, source_type,
                source_agent, confidence_score, verification_status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """
        
        product_id = await self.conn.fetchval(
            query,
            product_data['name'],
            product_data.get('description', ''),
            product_data['plant_part_id'],
            product_data.get('industry_sub_category_id'),
            product_data.get('benefits_advantages', []),
            product_data.get('keywords', []),
            product_data.get('source_url', ''),
            'ai_agent',
            'leaves_agent',
            product_data.get('confidence_score', 0.8),
            'ai_verified'
        )
        
        return product_id
        
    async def check_duplicate(self, name: str, plant_part_id: int) -> bool:
        """Check if product already exists"""
        count = await self.conn.fetchval(
            """
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name) = LOWER($1) AND plant_part_id = $2
            """,
            name, plant_part_id
        )
        return count > 0


async def run_leaves_agent():
    """Run the Hemp Leaves discovery agent"""
    
    # Database connection
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    # Connect with statement cache disabled for pgbouncer
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    
    # Create simple Supabase client
    supabase = SimpleSupabaseClient(conn)
    
    # Get Hemp Leaves plant part ID
    leaves_id = await supabase.get_plant_part_id('Hemp Leaves')
    if not leaves_id:
        logger.error("Hemp Leaves plant part not found in database!")
        await conn.close()
        return
        
    logger.info(f"Hemp Leaves plant part ID: {leaves_id}")
    
    # Get industry IDs
    industry_ids = await supabase.get_industry_ids()
    logger.info(f"Found {len(industry_ids)} industries")
    
    logger.info("Starting Hemp Leaves product discovery...")
    
    # Define hemp leaf products to add
    leaf_products = [
        {
            'name': 'Fresh Hemp Leaf Juice',
            'description': 'Cold-pressed fresh hemp leaf juice rich in chlorophyll and phytonutrients. Flash frozen to preserve nutrients.',
            'industry': 'Food & Beverage',
            'benefits': ['Rich in: Chlorophyll, Vitamin K, Iron', 'Flash frozen', 'Immune support', 'Alkalizing', 'Raw unprocessed']
        },
        {
            'name': 'Hemp Leaf Green Powder',
            'description': 'Freeze-dried hemp leaf powder for smoothies and supplements. High in protein, fiber, and antioxidants.',
            'industry': 'Health & Wellness',
            'benefits': ['Rich in: Protein, Fiber, Antioxidants', 'Freeze dried to preserve nutrients', 'Certified organic', 'Energy boost', 'Versatile superfood']
        },
        {
            'name': 'Hemp Leaf Detox Tea',
            'description': 'Organic hemp leaf tea blend for gentle detoxification. Contains whole dried leaves for maximum benefit.',
            'industry': 'Food & Beverage',
            'benefits': ['Detoxification support', 'Certified organic', 'Rich in: Antioxidants, Minerals', 'Digestive health', 'Caffeine-free']
        },
        {
            'name': 'Hemp Leaf Face Mask',
            'description': 'Rejuvenating face mask with hemp leaf extract. Rich in vitamins A, C, and E for skin health.',
            'industry': 'Cosmetics & Personal Care',
            'benefits': ['Rich in: Vitamin A, Vitamin C, Vitamin E', 'Skin health', 'Anti-aging', 'High chlorophyll content', 'Natural ingredients']
        },
        {
            'name': 'Hemp Leaf Salad Mix',
            'description': 'Fresh young hemp leaves for salads. Mild nutty flavor with exceptional nutritional profile.',
            'industry': 'Food & Beverage',
            'benefits': ['Fresh leaves', 'Rich in: Omega-3, Protein, Calcium', 'Certified organic', 'Supports: Heart Health, Bone Health', 'Farm fresh']
        },
        {
            'name': 'Hemp Leaf Animal Feed Supplement',
            'description': 'Dried hemp leaves for livestock and poultry feed. Improves animal health and product quality.',
            'industry': 'Agriculture',
            'benefits': ['Rich in: Protein, Essential Fatty Acids', 'Improves animal health', 'Natural supplement', 'Sustainable farming', 'Cost-effective']
        },
        {
            'name': 'Hemp Leaf Chlorophyll Extract',
            'description': 'Concentrated chlorophyll extract from hemp leaves. Supports blood health and detoxification.',
            'industry': 'Health & Wellness',
            'benefits': ['High chlorophyll content', 'Blood builder', 'Detoxification', 'Concentrated Extract', 'Oxygen support']
        },
        {
            'name': 'Hemp Leaf Smoothie Booster',
            'description': 'Pre-portioned hemp leaf powder packets for smoothies. Convenient single-serve nutrition boost.',
            'industry': 'Food & Beverage',
            'benefits': ['Rich in: Vitamins, Minerals, Fiber', 'Convenient packets', 'Energy boost', 'Metabolism support', 'Organic certified']
        },
        {
            'name': 'Hemp Leaf Skin Serum',
            'description': 'Anti-aging serum with hemp leaf extract and hyaluronic acid. Promotes collagen production.',
            'industry': 'Cosmetics & Personal Care',
            'benefits': ['Anti-aging', 'Rich in: Antioxidants, Vitamin E', 'Collagen support', 'Skin health', 'Fast absorption']
        },
        {
            'name': 'Hemp Leaf Poultice',
            'description': 'Traditional hemp leaf poultice for topical application. Helps with inflammation and skin conditions.',
            'industry': 'Health & Wellness',
            'benefits': ['Anti-inflammatory', 'Traditional remedy', 'Topical relief', 'Natural healing', 'Easy application']
        },
        {
            'name': 'Hemp Leaf Garden Mulch',
            'description': 'Composted hemp leaves for garden mulch. Suppresses weeds and enriches soil naturally.',
            'industry': 'Agriculture',
            'benefits': ['Weed suppression', 'Soil enrichment', 'Water retention', 'Sustainable gardening', 'Nutrient recycling']
        },
        {
            'name': 'Hemp Leaf Pet Supplement',
            'description': 'Ground hemp leaves for pet nutrition. Supports coat health and overall wellness in dogs and cats.',
            'industry': 'Pet Products',
            'benefits': ['Rich in: Omega Fatty Acids, Vitamins', 'Coat health', 'Joint support', 'Pet-safe formula', 'Natural supplement']
        }
    ]
    
    # Insert products
    added_count = 0
    for product_info in leaf_products:
        try:
            # Check if already exists
            if await supabase.check_duplicate(product_info['name'], leaves_id):
                logger.info(f"Product '{product_info['name']}' already exists, skipping")
                continue
                
            # Prepare product data
            product_data = {
                'name': product_info['name'],
                'description': product_info['description'],
                'plant_part_id': leaves_id,
                'industry_sub_category_id': industry_ids.get(product_info['industry']),
                'benefits_advantages': product_info['benefits'],
                'keywords': ['hemp leaves', 'nutrition', 'superfood', 'natural'],
                'source_url': 'https://nutritiondata.self.com',
                'confidence_score': 0.85
            }
            
            # Insert product
            product_id = await supabase.insert_product(product_data)
            logger.info(f"Added product: {product_info['name']} (ID: {product_id})")
            added_count += 1
            
            # Small delay to avoid overwhelming the database
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"Error adding product '{product_info['name']}': {e}")
            
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"HEMP LEAVES AGENT SUMMARY")  
    logger.info(f"{'='*60}")
    logger.info(f"Products added: {added_count}")
    
    # Check final count
    total_leaves = await conn.fetchval(
        "SELECT COUNT(*) FROM uses_products WHERE plant_part_id = $1",
        leaves_id
    )
    logger.info(f"Total hemp leaf products in database: {total_leaves}")
    
    await conn.close()
    logger.info("Database connection closed")


if __name__ == "__main__":
    asyncio.run(run_leaves_agent())