#!/usr/bin/env python3
"""
Add image generation capabilities to products
Supports multiple providers: DALL-E 3, Stable Diffusion, Gemini
"""

import os
import asyncio
import asyncpg
import requests
from urllib.parse import urlparse, unquote
from dotenv import load_dotenv
import logging
import time

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageGenerator:
    """Multi-provider image generator"""
    
    def __init__(self):
        self.openai_key = os.getenv('OPENAI_API_KEY')
        self.stability_key = os.getenv('STABILITY_API_KEY')
        self.gemini_key = os.getenv('GEMINI_API_KEY')
        
    def generate_with_dalle(self, prompt: str) -> str:
        """Generate image with DALL-E 3"""
        if not self.openai_key:
            return None
            
        try:
            from openai import OpenAI
            client = OpenAI(api_key=self.openai_key)
            
            response = client.images.generate(
                model="dall-e-3",
                prompt=f"Professional product photo: {prompt}. High quality, white background, commercial style.",
                size="1024x1024",
                quality="standard",
                n=1,
            )
            
            return response.data[0].url
            
        except Exception as e:
            logger.error(f"DALL-E error: {e}")
            return None
    
    def generate_with_stability(self, prompt: str) -> str:
        """Generate image with Stable Diffusion"""
        if not self.stability_key:
            return None
            
        try:
            response = requests.post(
                "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image",
                headers={
                    "Authorization": f"Bearer {self.stability_key}",
                    "Content-Type": "application/json",
                },
                json={
                    "text_prompts": [
                        {
                            "text": f"Professional product photo: {prompt}. High quality, white background, commercial style.",
                            "weight": 1
                        }
                    ],
                    "cfg_scale": 7,
                    "height": 1024,
                    "width": 1024,
                    "samples": 1,
                    "steps": 30,
                },
            )
            
            if response.status_code == 200:
                data = response.json()
                # Would need to save and upload the base64 image
                return "generated_image_url"
            
        except Exception as e:
            logger.error(f"Stability error: {e}")
            
        return None
    
    def scrape_image(self, product_name: str, plant_part: str) -> str:
        """Scrape image from web search"""
        # Simple Google Images search URL
        search_query = f"{product_name} hemp product"
        search_url = f"https://www.google.com/search?q={search_query}&tbm=isch"
        
        # In production, would use a proper scraping library
        # For now, return a placeholder
        return None
    
    async def generate_for_product(self, product: dict) -> str:
        """Generate or find image for a product"""
        
        prompt = f"{product['name']}. {product.get('description', '')}. Made from hemp {product.get('plant_part', '')}."
        
        # Try different methods in order
        methods = [
            ("DALL-E", lambda: self.generate_with_dalle(prompt)),
            ("Stability", lambda: self.generate_with_stability(prompt)),
            ("Web Scrape", lambda: self.scrape_image(product['name'], product.get('plant_part', '')))
        ]
        
        for method_name, method_func in methods:
            logger.info(f"Trying {method_name} for {product['name']}")
            image_url = method_func()
            if image_url:
                logger.info(f"✅ Generated image with {method_name}")
                return image_url
            time.sleep(1)  # Rate limiting
        
        return None


async def add_images_to_products(limit: int = 10):
    """Add images to products without them"""
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        logger.error("DATABASE_URL not found")
        return
        
    parsed = urlparse(database_url)
    
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    # Get products without images
    products = await conn.fetch("""
        SELECT p.id, p.name, p.description, pp.name as plant_part
        FROM uses_products p
        LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
        WHERE (p.image_url IS NULL OR p.image_url = '')
        AND p.source_agent = 'ai_discovery_agent'
        ORDER BY p.created_at DESC
        LIMIT $1
    """, limit)
    
    logger.info(f"Found {len(products)} AI products without images")
    
    generator = ImageGenerator()
    updated = 0
    
    for product in products:
        product_dict = dict(product)
        image_url = await generator.generate_for_product(product_dict)
        
        if image_url:
            # Update product with image
            await conn.execute("""
                UPDATE uses_products 
                SET image_url = $1
                WHERE id = $2
            """, image_url, product['id'])
            
            logger.info(f"✅ Added image to: {product['name']}")
            updated += 1
        else:
            logger.warning(f"❌ No image generated for: {product['name']}")
    
    await conn.close()
    
    print(f"\n✅ Updated {updated}/{len(products)} products with images")


def setup_image_automation():
    """Setup automated image generation"""
    
    print("\n" + "="*60)
    print("🖼️  IMAGE GENERATION SETUP")
    print("="*60)
    
    print("\n📋 Options for adding images:")
    
    print("\n1. **AI Generation** (Recommended)")
    print("   - DALL-E 3: $0.04 per image")
    print("   - Stable Diffusion: $0.002 per image")
    print("   - Gemini: Free tier available")
    
    print("\n2. **Web Scraping**")
    print("   - Free but requires attribution")
    print("   - May have copyright concerns")
    print("   - Quality varies")
    
    print("\n3. **Stock Photos API**")
    print("   - Unsplash: Free with attribution")
    print("   - Pexels: Free API")
    print("   - Shutterstock: Paid")
    
    print("\n🔧 To enable image generation:")
    print("1. Add to .env file:")
    print("   STABILITY_API_KEY=your_key_here")
    print("   # or use existing OPENAI_API_KEY")
    
    print("\n2. Add to automation cycle:")
    print("   - Edit start_automation.py")
    print("   - Add image generation step")
    
    print("\n3. Estimated costs:")
    print("   - 100 products/day = $4/day (DALL-E)")
    print("   - 100 products/day = $0.20/day (Stable Diffusion)")
    
    print("\n" + "="*60)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--generate':
        # Generate images for recent products
        asyncio.run(add_images_to_products(limit=5))
    else:
        # Show setup instructions
        setup_image_automation()