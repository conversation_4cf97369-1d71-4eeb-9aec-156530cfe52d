# Agent System Environment Configuration
# Copy this to .env and fill in your values

# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# AI Provider API Keys
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
GOOGLE_API_KEY=AIza...  # For search APIs
HUGGINGFACE_API_KEY=hf_...  # For open models

# Agent Configuration
AGENT_LOG_LEVEL=INFO
AGENT_MAX_CONCURRENT_TASKS=5
AGENT_DEFAULT_TIMEOUT=300  # seconds
AGENT_RETRY_ATTEMPTS=3
AGENT_RATE_LIMIT=60  # requests per minute

# Research Agent Settings
RESEARCH_AGENT_ENABLED=true
RESEARCH_AGENT_MAX_RESULTS=50
RESEARCH_AGENT_SEARCH_DEPTH=3
RESEARCH_AGENT_VERIFY_SOURCES=true
PUBMED_API_KEY=  # Optional for research
ARXIV_API_KEY=   # Optional for research

# Content Agent Settings
CONTENT_AGENT_ENABLED=true
CONTENT_AGENT_MIN_WORDS=500
CONTENT_AGENT_MAX_WORDS=2000
CONTENT_AGENT_STYLE=professional

# SEO Agent Settings
SEO_AGENT_ENABLED=true
SEO_AGENT_TARGET_REGIONS=US,CA,UK
SEO_AGENT_KEYWORD_LIMIT=100
SERPAPI_KEY=  # For search ranking data
AHREFS_API_KEY=  # Optional for SEO

# Outreach Agent Settings
OUTREACH_AGENT_ENABLED=true
OUTREACH_AGENT_EMAIL_LIMIT=50
OUTREACH_AGENT_FOLLOW_UP_DAYS=3
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Plant Part Agent Settings
PLANT_PART_AGENT_ENABLED=true
PLANT_PART_AGENT_PRODUCTS_PER_RUN=20
PLANT_PART_AGENT_CONFIDENCE_THRESHOLD=0.7

# Innovation Agent Settings
INNOVATION_AGENT_ENABLED=true
INNOVATION_AGENT_MIN_TRL=4  # Technology Readiness Level
INNOVATION_AGENT_FOCUS_AREAS=biotech,materials,energy

# Sustainability Agent Settings
SUSTAINABILITY_AGENT_ENABLED=true
SUSTAINABILITY_AGENT_MIN_IMPACT_SCORE=0.6
SUSTAINABILITY_AGENT_IMPACT_METRICS=carbon,water,waste

# Monitoring & Alerts
MONITORING_ENABLED=true
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
ERROR_THRESHOLD=5  # errors before alert

# Performance Settings
CACHE_ENABLED=true
CACHE_TTL=3600  # seconds
BATCH_SIZE=10
REQUEST_TIMEOUT=30  # seconds

# Security
API_RATE_LIMIT=100  # per minute per IP
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
JWT_SECRET=your-secret-key
ENCRYPTION_KEY=your-encryption-key

# Feature Flags
ENABLE_EXPERIMENTAL_AGENTS=false
ENABLE_AUTO_SCALING=false
ENABLE_DISTRIBUTED_PROCESSING=false
ENABLE_ML_OPTIMIZATION=false

# External Services
REDIS_URL=redis://localhost:6379
ELASTICSEARCH_URL=http://localhost:9200
VECTOR_DB_URL=http://localhost:8000  # For embeddings

# Development Settings
DEBUG=false
DEVELOPMENT_MODE=false
MOCK_AI_RESPONSES=false
LOG_SQL_QUERIES=false