#!/bin/bash
# Easy automation starter for Hemp Database

echo "======================================"
echo "🚀 HEMP DATABASE AUTOMATION"
echo "======================================"
echo ""
echo "This will start automated AI discovery"
echo "that runs every 6 hours to grow your"
echo "database with new hemp products."
echo ""
echo "Choose how to run:"
echo ""
echo "1. 🟢 Quick Test - Run once now"
echo "2. 🔵 Background - Run continuously"
echo "3. 📺 Screen - Run in screen session"
echo "4. 📊 Check Status"
echo ""
read -p "Select (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🧪 Running one cycle..."
        source venv_dedup/bin/activate
        python start_automation.py --run
        ;;
        
    2)
        echo ""
        echo "🚀 Starting background automation..."
        echo ""
        # Create logs directory if it doesn't exist
        mkdir -p logs
        
        # Start in background
        source venv_dedup/bin/activate
        nohup python start_automation.py --continuous > logs/automation_$(date +%Y%m%d).log 2>&1 &
        
        echo "✅ Automation started in background!"
        echo ""
        echo "📋 Commands:"
        echo "  View logs:    tail -f logs/automation_*.log"
        echo "  Check status: python monitor_automation.py"
        echo "  Stop:         pkill -f start_automation.py"
        echo ""
        ;;
        
    3)
        echo ""
        echo "📺 Starting in screen..."
        echo ""
        
        # Check if screen is installed
        if ! command -v screen &> /dev/null; then
            echo "❌ Screen not installed. Install with:"
            echo "   sudo apt-get install screen"
            echo ""
            echo "Or use option 2 for background mode."
            exit 1
        fi
        
        # Create screen session
        screen -dmS hemp-auto bash -c "source venv_dedup/bin/activate && python start_automation.py --continuous"
        
        echo "✅ Automation started in screen!"
        echo ""
        echo "📋 Commands:"
        echo "  View:    screen -r hemp-auto"
        echo "  Detach:  Press Ctrl+A, then D"
        echo "  List:    screen -ls"
        echo "  Stop:    screen -X -S hemp-auto quit"
        echo ""
        ;;
        
    4)
        echo ""
        echo "📊 Checking automation status..."
        source venv_dedup/bin/activate
        python monitor_automation.py
        ;;
esac