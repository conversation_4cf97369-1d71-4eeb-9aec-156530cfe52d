# 🚀 GitHub Actions Setup Guide - Hemp Database Automation

## 📋 Overview

This guide will help you configure GitHub Actions to restore automated hemp database population functionality. The system has been completely rebuilt with improved error handling, better logging, and more reliable operations.

## 🔐 Step 1: Configure GitHub Secrets

You **MUST** add these secrets to your GitHub repository for the automation to work:

### Required Secrets

1. **Go to your repository on GitHub**
2. **Click Settings → Secrets and variables → Actions**
3. **Click "New repository secret"** for each of the following:

#### SUPABASE_URL
- **Value**: `https://ktoqznqmlnxrtvubewyz.supabase.co`
- **Description**: Your Supabase project URL

#### SUPABASE_ANON_KEY
- **Value**: Your Supabase anonymous key (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI...`)
- **Description**: Public key for database access
- **Where to find**: Supabase Dashboard → Settings → API → anon public key

#### OPENAI_API_KEY (Optional but recommended)
- **Value**: Your OpenAI API key (starts with `sk-...`)
- **Description**: For AI-powered hemp product discovery
- **Where to get**: [OpenAI API Keys](https://platform.openai.com/api-keys)

### Optional Secrets (for enhanced functionality)

#### SUPABASE_SERVICE_ROLE_KEY
- **Value**: Your Supabase service role key
- **Description**: For admin-level database operations
- **Where to find**: Supabase Dashboard → Settings → API → service_role secret key

## 🔧 Step 2: Test the Setup

### Manual Test Run

1. **Go to Actions tab** in your GitHub repository
2. **Click "Automated Hemp Database Operations"**
3. **Click "Run workflow"**
4. **Select options**:
   - Operation: `discovery` (recommended for first test)
   - Max items: `10`
   - Test mode: `true` (for first run)
5. **Click "Run workflow"**

### Expected Results

✅ **Success indicators**:
- All jobs complete with green checkmarks
- "Hemp Database Operations Summary" shows successful results
- Reports are generated and uploaded as artifacts
- Database statistics are displayed

❌ **Failure indicators**:
- Red X marks on jobs
- Error messages about missing secrets
- "Environment validation failed" messages

## 📊 Step 3: Monitor Automated Runs

### Scheduled Operations

The system now runs automatically:

- **Every 6 hours**: Hemp product discovery (15 new products)
- **Daily at 8 AM UTC**: Health monitoring and reporting

### Operation Types

1. **Discovery**: Finds new hemp products and applications
2. **Research**: Discovers cutting-edge hemp innovations
3. **Monitoring**: Checks database health and statistics
4. **All**: Runs discovery + research + monitoring

## 🛠️ Step 4: Troubleshooting

### Common Issues

#### "Missing required secrets" Error
- **Solution**: Add all required secrets (see Step 1)
- **Check**: Repository Settings → Secrets and variables → Actions

#### "Database connection failed" Error
- **Solution**: Verify SUPABASE_URL and SUPABASE_ANON_KEY are correct
- **Test**: Check if you can access your Supabase dashboard

#### "Hemp CLI not found" Warning
- **Status**: Normal - the system uses Python directly as fallback
- **Impact**: None - operations continue normally

#### Import Errors
- **Solution**: The system auto-installs required dependencies
- **Check**: Look for "Install dependencies" step in logs

### Debug Mode

To get more detailed logs:
1. Run workflow manually
2. Set test mode to `true`
3. Check the "Hemp Operations" job logs
4. Download the generated reports from Artifacts

## 📈 Step 5: Verify Success

### Database Growth

Check your Supabase database for:
- New entries in `uses_products` table
- Entries in `hemp_agent_runs` table (operation logs)
- Updated timestamps on recent records

### GitHub Actions

Monitor the Actions tab for:
- Successful workflow runs (green checkmarks)
- Generated reports in Artifacts
- Workflow summaries with statistics

### Expected Growth Rate

- **Discovery runs**: ~15 new hemp products every 6 hours
- **Research runs**: ~8 innovative hemp applications
- **Daily total**: ~60-80 new database entries

## 🔄 Step 6: Advanced Configuration

### Customize Schedules

Edit `.github/workflows/automated-operations.yml`:

```yaml
schedule:
  - cron: '0 */4 * * *'  # Every 4 hours instead of 6
  - cron: '0 6 * * *'    # 6 AM instead of 8 AM
```

### Adjust Discovery Volume

Change the `MAX_ITEMS` in the workflow or use manual runs with custom values.

### Enable Additional Operations

Uncomment and configure other workflow files:
- `hemp-automation.yml.disabled`
- `monitoring-and-reporting.yml.disabled`

## 📊 Success Metrics

After setup, you should see:

- ✅ **0 failed workflow runs** (down from 605+)
- ✅ **Daily database growth** of 60-80 new hemp products
- ✅ **Automated reports** generated and stored
- ✅ **System health monitoring** active
- ✅ **Resource efficiency** - no wasted GitHub Actions minutes

## 🆘 Getting Help

If you encounter issues:

1. **Check the workflow logs** in GitHub Actions
2. **Download and review reports** from Artifacts
3. **Verify all secrets** are correctly configured
4. **Test with manual runs** before relying on scheduled runs
5. **Check Supabase logs** for database-related issues

## 🎉 Next Steps

Once the automation is working:

1. **Monitor growth** - Check database statistics daily
2. **Review discoveries** - Examine new hemp products being found
3. **Optimize operations** - Adjust schedules and parameters as needed
4. **Expand functionality** - Enable additional agents and workflows

---

**🌿 Your Hemp Database will now grow automatically with new products, applications, and innovations discovered every 6 hours!**
