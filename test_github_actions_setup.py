#!/usr/bin/env python3
"""
Test script to verify GitHub Actions setup for Hemp Database automation
"""

import os
import sys
from datetime import datetime

def test_environment_variables():
    """Test that required environment variables are set"""
    print("🔍 Testing Environment Variables...")
    
    required_vars = ['SUPABASE_URL', 'SUPABASE_ANON_KEY']
    optional_vars = ['SUPABASE_SERVICE_ROLE_KEY', 'OPENAI_API_KEY']
    
    missing_required = []
    available_optional = []
    
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: Set (length: {len(value)})")
        else:
            print(f"❌ {var}: Missing")
            missing_required.append(var)
    
    for var in optional_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: Set (length: {len(value)})")
            available_optional.append(var)
        else:
            print(f"⚪ {var}: Not set (optional)")
    
    return len(missing_required) == 0, missing_required

def test_python_dependencies():
    """Test that required Python packages are available"""
    print("\n🐍 Testing Python Dependencies...")
    
    dependencies = [
        ('requests', 'HTTP requests'),
        ('json', 'JSON handling (built-in)'),
        ('datetime', 'Date/time handling (built-in)'),
        ('os', 'Operating system interface (built-in)')
    ]
    
    optional_deps = [
        ('supabase', 'Supabase database client'),
        ('openai', 'OpenAI API client'),
        ('dotenv', 'Environment variable loading')
    ]
    
    missing_required = []
    available_optional = []
    
    for package, description in dependencies:
        try:
            __import__(package)
            print(f"✅ {package}: Available - {description}")
        except ImportError:
            print(f"❌ {package}: Missing - {description}")
            missing_required.append(package)
    
    for package, description in optional_deps:
        try:
            __import__(package)
            print(f"✅ {package}: Available - {description}")
            available_optional.append(package)
        except ImportError:
            print(f"⚪ {package}: Not available - {description}")
    
    return len(missing_required) == 0, missing_required, available_optional

def test_supabase_connection():
    """Test connection to Supabase database"""
    print("\n🗄️ Testing Supabase Connection...")
    
    try:
        from supabase import create_client
        
        url = os.environ.get('SUPABASE_URL')
        key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY') or os.environ.get('SUPABASE_ANON_KEY')
        
        if not url or not key:
            print("❌ Missing Supabase credentials")
            return False
        
        print(f"🔗 Connecting to: {url}")
        client = create_client(url, key)
        
        # Test basic query
        result = client.table('uses_products').select('id', count='exact').limit(1).execute()
        
        print(f"✅ Connection successful!")
        print(f"📊 Database contains {result.count} products")
        return True
        
    except ImportError:
        print("⚪ Supabase library not available - will use fallback methods")
        return True  # Not a failure, just different mode
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def test_file_permissions():
    """Test that we can create files and directories"""
    print("\n📁 Testing File Permissions...")
    
    try:
        # Test directory creation
        os.makedirs('test_reports', exist_ok=True)
        print("✅ Can create directories")
        
        # Test file creation
        test_file = 'test_reports/test_file.txt'
        with open(test_file, 'w') as f:
            f.write(f"Test file created at {datetime.now()}")
        print("✅ Can create files")
        
        # Test file reading
        with open(test_file, 'r') as f:
            content = f.read()
        print("✅ Can read files")
        
        # Cleanup
        os.remove(test_file)
        os.rmdir('test_reports')
        print("✅ Can delete files and directories")
        
        return True
        
    except Exception as e:
        print(f"❌ File operations failed: {e}")
        return False

def test_hemp_operation_script():
    """Test that the hemp operation script can be imported and run"""
    print("\n🌿 Testing Hemp Operation Script...")
    
    try:
        # Add the scripts directory to path
        scripts_dir = os.path.join(os.path.dirname(__file__), '.github', 'scripts')
        if os.path.exists(scripts_dir):
            sys.path.insert(0, scripts_dir)
        
        # Try to import the script
        import run_hemp_operation
        print("✅ Hemp operation script can be imported")
        
        # Test basic functions
        if hasattr(run_hemp_operation, 'hemp_product_discovery'):
            print("✅ Hemp product discovery function available")
        
        if hasattr(run_hemp_operation, 'get_supabase_client'):
            print("✅ Supabase client function available")
        
        return True
        
    except Exception as e:
        print(f"❌ Hemp operation script test failed: {e}")
        return False

def main():
    """Run all tests and provide summary"""
    print("🚀 GitHub Actions Hemp Database Setup Test")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Python Dependencies", test_python_dependencies),
        ("Supabase Connection", test_supabase_connection),
        ("File Permissions", test_file_permissions),
        ("Hemp Operation Script", test_hemp_operation_script)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if test_name == "Python Dependencies":
                success, missing, available = test_func()
                results.append((test_name, success, missing if not success else None))
            elif test_name == "Environment Variables":
                success, missing = test_func()
                results.append((test_name, success, missing if not success else None))
            else:
                success = test_func()
                results.append((test_name, success, None))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False, str(e)))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success, details in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if not success and details:
            print(f"    Details: {details}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your GitHub Actions setup should work correctly.")
        print("\nNext steps:")
        print("1. Add the required secrets to GitHub repository")
        print("2. Run the automated workflow manually to test")
        print("3. Monitor the scheduled runs")
    else:
        print(f"\n⚠️ {total - passed} TESTS FAILED")
        print("Please fix the issues above before running GitHub Actions.")
        print("\nCommon fixes:")
        print("- Add missing GitHub secrets")
        print("- Install missing Python packages")
        print("- Check Supabase credentials")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
