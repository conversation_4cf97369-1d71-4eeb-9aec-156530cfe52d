#!/usr/bin/env python3
"""
Check current image status for products
"""

import asyncio
import asyncpg
from urllib.parse import urlparse, unquote
from dotenv import load_dotenv
import os

load_dotenv()


async def check_image_status():
    """Check how many products have images"""
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL not found")
        return
        
    parsed = urlparse(database_url)
    
    try:
        conn = await asyncpg.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            database=parsed.path[1:],
            user=parsed.username,
            password=unquote(parsed.password),
            ssl='require',
            statement_cache_size=0
        )
        
        print("\n" + "="*60)
        print("🖼️  PRODUCT IMAGE STATUS")
        print("="*60)
        
        # Check if products table has image fields
        columns = await conn.fetch("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'uses_products' 
            AND column_name LIKE '%image%'
        """)
        
        if columns:
            print("\n📊 Image columns found:")
            for col in columns:
                print(f"  - {col['column_name']}")
                
            # Count products with images
            for col in columns:
                col_name = col['column_name']
                count = await conn.fetchval(f"""
                    SELECT COUNT(*) FROM uses_products 
                    WHERE {col_name} IS NOT NULL AND {col_name} != ''
                """)
                print(f"\n  Products with {col_name}: {count}")
        else:
            print("\n⚠️  No image columns found in uses_products table")
            
        # Check companies with logos
        print("\n\n🏢 COMPANY LOGOS:")
        total_companies = await conn.fetchval("SELECT COUNT(*) FROM hemp_companies")
        companies_with_logos = await conn.fetchval("""
            SELECT COUNT(*) FROM hemp_companies 
            WHERE logo_url IS NOT NULL AND logo_url != ''
        """)
        print(f"  Companies with logos: {companies_with_logos}/{total_companies}")
        
        # Check research with images
        print("\n\n📚 RESEARCH IMAGES:")
        total_research = await conn.fetchval("SELECT COUNT(*) FROM research_entries")
        research_with_images = await conn.fetchval("""
            SELECT COUNT(*) FROM research_entries 
            WHERE image_url IS NOT NULL AND image_url != ''
        """)
        print(f"  Research with images: {research_with_images}/{total_research}")
        
        # Check recent AI products
        print("\n\n🤖 AI-GENERATED PRODUCTS:")
        ai_products = await conn.fetchval("""
            SELECT COUNT(*) FROM uses_products 
            WHERE source_agent = 'ai_discovery_agent'
        """)
        print(f"  Total AI products: {ai_products}")
        print(f"  Note: AI agents currently generate text only, not images")
        
        print("\n" + "="*60)
        print("💡 RECOMMENDATIONS:")
        print("="*60)
        print("\n1. Product images are not currently being generated")
        print("2. Consider adding image generation to the automation:")
        print("   - Use DALL-E 3 or Stable Diffusion APIs")
        print("   - Or scrape product images from hemp websites")
        print("3. Company logos: {:.1f}% coverage".format(
            (companies_with_logos/total_companies*100) if total_companies > 0 else 0
        ))
        print("4. Research images: {:.1f}% coverage".format(
            (research_with_images/total_research*100) if total_research > 0 else 0
        ))
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(check_image_status())