#!/usr/bin/env python3
"""
Test Gemini and DeepSeek for hemp product discovery
Shows the quality of responses from each provider
"""

import os
from dotenv import load_dotenv
from ai_providers.multi_provider import get_ai_client

# Load environment variables
load_dotenv()


def test_providers_quality():
    """Test quality of responses from each provider"""
    
    ai = get_ai_client()
    
    print("\n" + "="*70)
    print("🧪 TESTING AI PROVIDERS FOR HEMP PRODUCT DISCOVERY")
    print("="*70)
    
    # Test prompt for innovative products
    test_cases = [
        ("Terpenes", "innovative"),
        ("Cannabinoids", "medical"),
        ("Hemp Fiber", "sustainable")
    ]
    
    for plant_part, focus in test_cases:
        print(f"\n\n🌿 {plant_part} ({focus} focus)")
        print("-" * 50)
        
        # Test DeepSeek
        print("\n📡 DeepSeek Response:")
        products = ai.generate_hemp_products(
            plant_part=plant_part,
            count=2,
            innovative=(focus == "innovative"),
            preferred_provider="deepseek"
        )
        
        if products:
            for i, product in enumerate(products, 1):
                print(f"\n{i}. {product.get('name', 'Unknown')}")
                print(f"   Description: {product.get('description', 'N/A')[:100]}...")
                print(f"   Industry: {product.get('industry', 'N/A')}")
                if 'benefits' in product:
                    print(f"   Benefits: {', '.join(product['benefits'][:3])}")
        
        # Test Gemini
        print("\n\n🌟 Gemini Response:")
        products = ai.generate_hemp_products(
            plant_part=plant_part,
            count=2,
            innovative=(focus == "innovative"),
            preferred_provider="gemini"
        )
        
        if products:
            for i, product in enumerate(products, 1):
                print(f"\n{i}. {product.get('name', 'Unknown')}")
                print(f"   Description: {product.get('description', 'N/A')[:100]}...")
                print(f"   Industry: {product.get('industry', 'N/A')}")
                if 'benefits' in product:
                    print(f"   Benefits: {', '.join(product['benefits'][:3])}")
    
    print("\n\n" + "="*70)
    print("📊 PROVIDER COMPARISON")
    print("="*70)
    
    print("\n✅ DeepSeek Strengths:")
    print("  • Fast response times")
    print("  • Good technical detail")
    print("  • Consistent JSON formatting")
    
    print("\n✅ Gemini Strengths:")
    print("  • Creative and innovative ideas")
    print("  • Comprehensive descriptions")
    print("  • Strong on sustainability aspects")
    
    print("\n💡 Recommendation:")
    print("  Use BOTH providers with fallback for best results!")
    print("  Primary: DeepSeek (reliable), Fallback: Gemini (creative)")


def test_raw_responses():
    """Test raw responses to debug JSON issues"""
    
    print("\n\n🔍 TESTING RAW RESPONSES")
    print("="*50)
    
    ai = get_ai_client()
    
    prompt = """Generate 2 innovative hemp terpene products in this exact JSON format:
[
  {
    "name": "Product Name Here",
    "description": "Description here",
    "industry": "Industry Category",
    "benefits": ["Benefit 1", "Benefit 2", "Benefit 3"]
  }
]"""
    
    # Test DeepSeek
    print("\nDeepSeek raw response:")
    response = ai.generate(prompt, temperature=0.5, preferred_provider="deepseek")
    if response.success:
        print(response.content)
    else:
        print(f"Error: {response.error}")
    
    # Test Gemini
    print("\n\nGemini raw response:")
    response = ai.generate(prompt, temperature=0.5, preferred_provider="gemini")
    if response.success:
        print(response.content)
    else:
        print(f"Error: {response.error}")


if __name__ == "__main__":
    test_providers_quality()
    test_raw_responses()