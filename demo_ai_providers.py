#!/usr/bin/env python3
"""
Demo: Show DeepSeek and Gemini working for hemp product discovery
"""

import os
import json
from dotenv import load_dotenv
from ai_providers.multi_provider import get_ai_client

load_dotenv()


def demo_providers():
    """Demo both AI providers"""
    
    print("\n" + "="*70)
    print("🤖 AI PROVIDERS DEMO FOR HEMP DATABASE")
    print("="*70)
    
    ai = get_ai_client()
    providers = ai.get_available_providers()
    
    print(f"\n✅ Available providers: {providers}")
    print("\nGenerating innovative hemp products using AI...\n")
    
    # Simple prompt that works well
    prompt = """List 3 innovative hemp terpene products. For each product provide:
1. Product name
2. Brief description (1-2 sentences)
3. Key innovation aspect

Format as a numbered list."""
    
    # Test DeepSeek
    print("📡 DEEPSEEK RESPONSE:")
    print("-" * 50)
    response = ai.generate(prompt, temperature=0.7, preferred_provider="deepseek")
    if response.success:
        print(response.content)
        print(f"\n✅ Provider: {response.provider}")
    else:
        print(f"❌ Error: {response.error}")
    
    print("\n")
    
    # Test Gemini  
    print("🌟 GEMINI RESPONSE:")
    print("-" * 50)
    response = ai.generate(prompt, temperature=0.7, preferred_provider="gemini")
    if response.success:
        print(response.content)
        print(f"\n✅ Provider: {response.provider}")
    else:
        print(f"❌ Error: {response.error}")
    
    # Show how to use for database
    print("\n\n" + "="*70)
    print("💡 HOW THIS HELPS THE HEMP DATABASE:")
    print("="*70)
    
    print("\n1. 🚀 Rapid Product Discovery")
    print("   - Generate dozens of product ideas in minutes")
    print("   - Cover emerging technologies and innovations")
    print("   - Fill gaps in underrepresented categories")
    
    print("\n2. 🎯 Targeted Growth")
    print("   - Focus on plant parts with few products (Terpenes: 10)")
    print("   - Add cutting-edge products to stay current")
    print("   - Discover niche applications")
    
    print("\n3. 🔄 Continuous Updates")
    print("   - Agents can run daily to find new products")
    print("   - AI stays current with latest research")
    print("   - Automatic database growth")
    
    print("\n4. 📊 Current Opportunity Areas:")
    print("   - Terpenes: Only 10 products (needs 20+)")
    print("   - Cannabinoids: 31 products (could reach 50)")
    print("   - Hemp Roots: 19 products (untapped potential)")
    
    print("\n✨ Both DeepSeek and Gemini are configured and ready!")
    print("🎯 Next: Run 'python launch_ai_powered_agent.py' to add products")
    print("\n" + "="*70)


if __name__ == "__main__":
    demo_providers()