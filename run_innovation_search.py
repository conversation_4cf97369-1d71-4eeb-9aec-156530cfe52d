#!/usr/bin/env python3
"""
Run innovation search to find cutting-edge hemp products
Searches for advanced materials and innovative applications
"""

import asyncio
import logging
from datetime import datetime
from urllib.parse import urlparse, unquote
import asyncpg
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleSupabaseClient:
    """Minimal Supabase client for agent use"""
    
    def __init__(self, conn):
        self.conn = conn
        
    async def get_plant_part_id(self, name: str) -> int:
        """Get plant part ID by name"""
        result = await self.conn.fetchval(
            "SELECT id FROM plant_parts WHERE name = $1",
            name
        )
        return result
        
    async def get_industry_ids(self) -> dict:
        """Get all industry subcategory IDs"""
        rows = await self.conn.fetch(
            "SELECT id, name FROM industry_sub_categories"
        )
        return {row['name']: row['id'] for row in rows}
        
    async def insert_product(self, product_data: dict) -> int:
        """Insert a new product"""
        query = """
            INSERT INTO uses_products (
                name, description, plant_part_id, industry_sub_category_id,
                benefits_advantages, keywords, source_url, source_type,
                source_agent, confidence_score, verification_status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """
        
        product_id = await self.conn.fetchval(
            query,
            product_data['name'],
            product_data.get('description', ''),
            product_data['plant_part_id'],
            product_data.get('industry_sub_category_id'),
            product_data.get('benefits_advantages', []),
            product_data.get('keywords', []),
            product_data.get('source_url', ''),
            'ai_agent',
            'innovation_agent',
            product_data.get('confidence_score', 0.9),
            'ai_verified'
        )
        
        return product_id
        
    async def check_duplicate(self, name: str, plant_part_id: int) -> bool:
        """Check if product already exists"""
        count = await self.conn.fetchval(
            """
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name) = LOWER($1) AND plant_part_id = $2
            """,
            name, plant_part_id
        )
        return count > 0


# Define innovative hemp products to search for and add
INNOVATIVE_PRODUCTS = [
    # Advanced Materials
    {
        'name': 'Hemp Graphene Supercapacitor',
        'description': 'High-performance energy storage device using hemp-derived graphene nanosheets. Outperforms traditional lithium batteries in power density.',
        'plant_part': 'Hemp Bast (Fiber)',
        'industry': 'Electronics',
        'benefits': ['10x power density', 'Sustainable production', 'Cost-effective', 'Fast charging', 'Long lifecycle']
    },
    {
        'name': 'Hemp Aerogel Insulation',
        'description': 'Ultra-lightweight aerogel insulation made from hemp fibers. Superior thermal performance with minimal environmental impact.',
        'plant_part': 'Hemp Bast (Fiber)',
        'industry': 'Construction Materials',
        'benefits': ['R-value 50+', '99% air', 'Fire resistant', 'Moisture regulating', 'Carbon negative']
    },
    {
        'name': 'Hemp Quantum Dots',
        'description': 'Fluorescent quantum dots synthesized from hemp for medical imaging and LED displays. Non-toxic alternative to heavy metal quantum dots.',
        'plant_part': 'Hemp Bast (Fiber)',
        'industry': 'Electronics',
        'benefits': ['Biocompatible', 'Bright fluorescence', 'Tunable emission', 'Non-toxic', 'Low cost synthesis']
    },
    
    # Biomedical Applications
    {
        'name': 'Hemp Nanofiber Wound Dressing',
        'description': 'Electrospun hemp nanofiber dressing with antimicrobial properties. Promotes faster healing and prevents infection.',
        'plant_part': 'Hemp Bast (Fiber)',
        'industry': 'Medical Devices',
        'benefits': ['Antimicrobial', 'Breathable', 'Biodegradable', 'Accelerated healing', 'Non-allergenic']
    },
    {
        'name': 'Hemp Stem Cell Growth Scaffold',
        'description': '3D bioprinted scaffold from hemp proteins for tissue engineering. Supports stem cell growth and differentiation.',
        'plant_part': 'Hemp Seed',
        'industry': 'Medical Devices',
        'benefits': ['Biocompatible', 'Cell adhesion', '3D structure', 'Biodegradable', 'Tissue regeneration']
    },
    
    # Space Technology
    {
        'name': 'Hemp Radiation Shielding Composite',
        'description': 'Lightweight composite material for spacecraft radiation shielding. Hemp fibers combined with specialized polymers.',
        'plant_part': 'Hemp Bast (Fiber)',
        'industry': 'Aerospace',
        'benefits': ['Radiation blocking', '50% lighter', 'Impact resistant', 'Temperature stable', 'Space-qualified']
    },
    
    # Environmental Solutions
    {
        'name': 'Hemp Biochar Water Filter',
        'description': 'Activated biochar from hemp stalks for heavy metal removal from water. Removes 99% of lead, mercury, and cadmium.',
        'plant_part': 'Hemp Hurd (Shivs)',
        'industry': 'Environmental Services',
        'benefits': ['99% metal removal', 'Regenerable', 'pH neutral', 'High capacity', 'Carbon sequestration']
    },
    {
        'name': 'Hemp Mycelium Air Purifier',
        'description': 'Living air purification system combining hemp substrate with mycelium. Breaks down VOCs and produces oxygen.',
        'plant_part': 'Hemp Hurd (Shivs)',
        'industry': 'Environmental Services',
        'benefits': ['VOC removal', 'Oxygen production', 'Self-sustaining', 'No filters needed', 'Living system']
    },
    
    # Energy Applications
    {
        'name': 'Hemp Biohydrogen Reactor',
        'description': 'Microbial fuel cell using hemp biomass to produce hydrogen gas. Clean energy from agricultural waste.',
        'plant_part': 'Hemp Leaves',
        'industry': 'Energy',
        'benefits': ['Green hydrogen', 'Waste to energy', 'Carbon neutral', 'Continuous production', 'Scalable']
    },
    {
        'name': 'Hemp Piezoelectric Fabric',
        'description': 'Smart fabric that generates electricity from movement. Hemp fibers coated with piezoelectric polymers.',
        'plant_part': 'Hemp Bast (Fiber)',
        'industry': 'Textiles',
        'benefits': ['Energy harvesting', 'Wearable power', 'Flexible', 'Washable', 'IoT integration']
    },
    
    # Robotics & AI
    {
        'name': 'Hemp Soft Robotics Actuator',
        'description': 'Biodegradable soft robotic actuators made from hemp fiber composites. For agricultural and medical robots.',
        'plant_part': 'Hemp Bast (Fiber)',
        'industry': 'Robotics',
        'benefits': ['Biodegradable', 'Soft & safe', 'Programmable stiffness', 'Low cost', 'Biocompatible']
    },
    
    # Food Technology
    {
        'name': 'Hemp Protein Meat Scaffold',
        'description': '3D scaffold for cultured meat production using hemp proteins. Provides structure for lab-grown meat.',
        'plant_part': 'Hemp Seed',
        'industry': 'Food & Beverage',
        'benefits': ['Cell culture support', 'Edible scaffold', 'Neutral taste', 'High protein', 'Sustainable']
    }
]


async def run_innovation_search():
    """Search for and add innovative hemp products"""
    
    # Database connection
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    # Connect with statement cache disabled for pgbouncer
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    logger.info("Connected to database")
    logger.info("\n🚀 INNOVATION SEARCH AGENT")
    logger.info("="*60)
    logger.info("Searching for cutting-edge hemp applications...")
    
    # Create simple Supabase client
    supabase = SimpleSupabaseClient(conn)
    
    # Get industry IDs
    industry_ids = await supabase.get_industry_ids()
    
    # Get plant part IDs
    plant_parts = {}
    plant_part_names = ['Hemp Bast (Fiber)', 'Hemp Seed', 'Hemp Hurd (Shivs)', 'Hemp Leaves', 'Hemp Flowers', 'Hemp Roots', 'Terpenes']
    for name in plant_part_names:
        part_id = await supabase.get_plant_part_id(name)
        if part_id:
            plant_parts[name] = part_id
    
    logger.info(f"Found {len(plant_parts)} plant parts")
    
    # Add innovative products
    added_count = 0
    categories = {}
    
    for product_info in INNOVATIVE_PRODUCTS:
        try:
            plant_part_id = plant_parts.get(product_info['plant_part'])
            if not plant_part_id:
                logger.warning(f"Plant part '{product_info['plant_part']}' not found, skipping")
                continue
            
            # Check if already exists
            if await supabase.check_duplicate(product_info['name'], plant_part_id):
                logger.info(f"Product '{product_info['name']}' already exists, skipping")
                continue
                
            # Track categories
            category = product_info.get('industry', 'Unknown')
            categories[category] = categories.get(category, 0) + 1
            
            # Prepare product data
            product_data = {
                'name': product_info['name'],
                'description': product_info['description'],
                'plant_part_id': plant_part_id,
                'industry_sub_category_id': industry_ids.get(product_info['industry']),
                'benefits_advantages': product_info['benefits'],
                'keywords': ['innovation', 'advanced', 'hemp', 'sustainable', 'technology'],
                'source_url': 'https://hemp-innovation.org',
                'confidence_score': 0.9
            }
            
            # Insert product
            product_id = await supabase.insert_product(product_data)
            logger.info(f"✅ Added: {product_info['name']} (ID: {product_id})")
            added_count += 1
            
            # Small delay
            await asyncio.sleep(0.3)
            
        except Exception as e:
            logger.error(f"Error adding '{product_info['name']}': {e}")
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"INNOVATION SEARCH SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Innovative products added: {added_count}")
    logger.info(f"\nCategories discovered:")
    for category, count in sorted(categories.items()):
        logger.info(f"  - {category}: {count} products")
    
    # Get new total
    total_products = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
    logger.info(f"\nTotal products in database: {total_products}")
    
    # Show some innovation highlights
    if added_count > 0:
        logger.info(f"\n🌟 Innovation Highlights:")
        logger.info("  - Graphene supercapacitors from hemp")
        logger.info("  - Quantum dots for medical imaging")
        logger.info("  - Radiation shielding for spacecraft")
        logger.info("  - Living air purification systems")
        logger.info("  - Energy harvesting fabrics")
    
    await conn.close()
    logger.info("\nDatabase connection closed")


if __name__ == "__main__":
    asyncio.run(run_innovation_search())