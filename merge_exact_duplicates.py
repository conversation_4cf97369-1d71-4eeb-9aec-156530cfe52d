#!/usr/bin/env python3
"""Automatically merge exact duplicates found in the database."""
import asyncio
import asyncpg
from urllib.parse import urlparse, unquote
import json
from datetime import datetime

async def merge_exact_duplicates():
    """Find and merge exact (100%) duplicates."""
    # Database URL
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    # Connect to database (disable statement cache for pgbouncer)
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0  # Disable for pgbouncer compatibility
    )
    
    print("Connected to database")
    
    # Find exact duplicates (100% name match)
    query = """
        SELECT p1.id as id1, p1.name as name1, p1.plant_part_id as part1,
               p2.id as id2, p2.name as name2, p2.plant_part_id as part2,
               p1.created_at, p2.created_at as created_at2
        FROM uses_products p1
        JOIN uses_products p2 ON p1.name = p2.name 
            AND p1.id < p2.id
            AND p1.plant_part_id = p2.plant_part_id
        WHERE p1.canonical_product_id IS NULL 
            AND p2.canonical_product_id IS NULL
        ORDER BY p1.id, p2.id
    """
    
    duplicates = await conn.fetch(query)
    print(f"\nFound {len(duplicates)} exact duplicate pairs")
    
    # Merge duplicates
    merged_count = 0
    for dup in duplicates:
        id1, name1, id2, name2 = dup['id1'], dup['name1'], dup['id2'], dup['name2']
        
        # Keep the older product as canonical
        if dup['created_at'] <= dup['created_at2']:
            canonical_id, duplicate_id = id1, id2
        else:
            canonical_id, duplicate_id = id2, id1
        
        print(f"\nMerging: '{name1}' (ID: {id2}) into canonical (ID: {id1})")
        
        try:
            # Start transaction
            async with conn.transaction():
                # Update the duplicate to point to canonical
                await conn.execute("""
                    UPDATE uses_products 
                    SET canonical_product_id = $1,
                        verification_status = 'ai_verified',
                        merge_history = jsonb_build_array(
                            jsonb_build_object(
                                'merged_id', $2::text,
                                'merged_at', $3::text,
                                'reason', 'Exact name and plant part match'
                            )
                        )
                    WHERE id = $2
                """, canonical_id, duplicate_id, datetime.now().isoformat())
                
                # Record in duplicate_candidates table
                await conn.execute("""
                    INSERT INTO duplicate_candidates 
                    (product_id_1, product_id_2, similarity_score, similarity_type, 
                     reviewed, action_taken, reviewed_by, reviewed_at)
                    VALUES ($1, $2, 1.0, 'exact_match', true, 'merged', 'autonomous_system', CURRENT_TIMESTAMP)
                    ON CONFLICT (product_id_1, product_id_2) DO NOTHING
                """, canonical_id, duplicate_id)
                
                # Add version history entry
                await conn.execute("""
                    INSERT INTO uses_products_versions 
                    (product_id, version_number, data, changed_by, change_type, change_reason)
                    VALUES ($1, 1, $2, 'deduplication_system', 'merge', 
                            'Merged as duplicate of product ' || $3::text)
                """, duplicate_id, json.dumps({
                    'status': 'merged',
                    'canonical_id': canonical_id,
                    'original_name': name2
                }), str(canonical_id))
                
                merged_count += 1
                print(f"✅ Successfully merged")
                
        except Exception as e:
            print(f"❌ Error merging: {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"MERGE SUMMARY")
    print(f"{'='*60}")
    print(f"Total exact duplicates merged: {merged_count}")
    
    # Check remaining duplicates
    remaining = await conn.fetchval("""
        SELECT COUNT(DISTINCT p2.id)
        FROM uses_products p1
        JOIN uses_products p2 ON p1.name = p2.name 
            AND p1.id < p2.id
            AND p1.plant_part_id = p2.plant_part_id
        WHERE p1.canonical_product_id IS NULL 
            AND p2.canonical_product_id IS NULL
    """)
    
    print(f"Remaining exact duplicates: {remaining}")
    
    await conn.close()
    print("\nDatabase connection closed")

if __name__ == "__main__":
    asyncio.run(merge_exact_duplicates())