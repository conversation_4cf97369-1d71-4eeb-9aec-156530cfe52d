{"permissions": {"allow": ["mcp__supabase-mcp-server__get_project", "mcp__supabase-mcp-server__list_tables", "mcp__supabase-mcp-server__execute_sql", "mcp__Context7__get-library-docs", "mcp__github__search_issues", "Bash(gh workflow list:*)", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__search_files", "mcp__desktop-commander__read_file", "mcp__desktop-commander__read_multiple_files", "mcp__supabase-mcp-server__list_projects", "Bash(find:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(git add:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(touch:*)", "mcp__github__list_pull_requests", "mcp__github__get_pull_request_files", "Bash(git fetch:*)", "Bash(ls:*)", "Bash(git commit:*)", "<PERSON><PERSON>(curl:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__supabase-mcp-server__list_migrations", "<PERSON><PERSON>(mkdir:*)", "mcp__supabase-mcp-server__apply_migration", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_click", "<PERSON><PERSON>(nslookup:*)", "<PERSON><PERSON>(host:*)", "Bash(ping:*)", "Bash(bash:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:cloud.google.com)", "mcp__Context7__resolve-library-id", "Bash(npm install:*)", "Bash(npm run check:*)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__move_file", "mcp__desktop-commander__write_file", "Bash(pip3 install:*)", "mcp__brave-search__brave_web_search", "Bash(pip install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./run_add_products.sh:*)", "<PERSON><PERSON>(source:*)", "Bash(npm run:*)", "mcp__supabase-mcp-server__list_edge_functions", "mcp__supabase-mcp-server__get_logs", "Bash(cp:*)", "mcp__github__get_file_contents", "mcp__github__search_code", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_hover", "Bash(export:*)", "<PERSON><PERSON>(poetry init:*)", "Bash(git push:*)", "Bash(pip3 list:*)", "mcp__supabase-mcp-server__deploy_edge_function", "Bash(npm audit:*)", "<PERSON><PERSON>(poetry show:*)", "Bash(npm update:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(gh run list:*)", "mcp__github__list_issues", "mcp__github__get_pull_request", "mcp__github__add_issue_comment", "Bash(git checkout:*)", "Bash(git stash:*)", "Bash(git merge:*)", "mcp__github__search_repositories", "Bash(git pull:*)", "Bash(./hemp db:*)", "Bash(./hemp --help)", "Bash(npx supabase:*)", "mcp__supabase-mcp-server__get_project_url", "mcp__github__merge_pull_request", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(sed:*)", "Bash(.venv/Scripts/python:*)", "Bash(for f in hemp-automation.yml image-generation.yml monitoring.yml status-check.yml weekly-summary.yml)", "Bash(do [ -f \"$f\" ])", "<PERSON><PERSON>(echo:*)", "Bash(done)", "<PERSON><PERSON>(cat:*)", "Bash(./hemp agent research \"hemp building materials\" --features basic --ai-provider deepseek --max-results 2)", "WebFetch(domain:docs.anthropic.com)", "Bash(pip --version)", "Ba<PERSON>(pip3:*)", "Bash(git ls-tree:*)", "Bash(ss:*)", "Bash(git restore:*)", "<PERSON><PERSON>(test:*)", "Bash(NODE_TLS_REJECT_UNAUTHORIZED=0 npm run db:push)", "<PERSON><PERSON>(timeout 10 npm run dev)", "Bash(npm test:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(psql:*)", "Bash(kill:*)", "Bash(git reset:*)"], "deny": []}}