#!/usr/bin/env python3
"""
Simple Agent Performance Dashboard
Monitors autonomous agent activity and database growth
"""

import asyncio
import asyncpg
from urllib.parse import urlparse, unquote
from datetime import datetime, timedelta
import json

async def generate_dashboard():
    """Generate agent performance dashboard"""
    
    # Database connection
    database_url = 'postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres?sslmode=require'
    parsed = urlparse(database_url)
    
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    print("\n" + "="*60)
    print("🤖 AUTONOMOUS AGENT DASHBOARD")
    print("="*60)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 1. Agent Activity Summary
    print("📊 AGENT ACTIVITY (Last 7 Days):")
    agent_stats = await conn.fetch("""
        SELECT 
            source_agent,
            COUNT(*) as products_added,
            MIN(created_at) as first_product,
            MAX(created_at) as last_product,
            AVG(confidence_score) as avg_confidence
        FROM uses_products
        WHERE source_type = 'ai_agent' 
            AND created_at > NOW() - INTERVAL '7 days'
        GROUP BY source_agent
        ORDER BY products_added DESC
    """)
    
    if agent_stats:
        for agent in agent_stats:
            print(f"\n   🤖 {agent['source_agent'] or 'Unknown Agent'}:")
            print(f"      Products Added: {agent['products_added']}")
            print(f"      Avg Confidence: {agent['avg_confidence']:.2f}" if agent['avg_confidence'] else "      Avg Confidence: N/A")
            print(f"      Last Active: {agent['last_product'].strftime('%Y-%m-%d %H:%M')}")
    else:
        print("   No agent activity in the last 7 days")
    
    # 2. Database Growth Metrics
    print("\n\n📈 DATABASE GROWTH:")
    growth_data = await conn.fetch("""
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as products_added,
            COUNT(DISTINCT source_agent) as active_agents
        FROM uses_products
        WHERE created_at > NOW() - INTERVAL '7 days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    """)
    
    for day in growth_data:
        print(f"   {day['date']}: +{day['products_added']} products ({day['active_agents']} agents)")
    
    # 3. Plant Part Progress
    print("\n\n🌱 PLANT PART COVERAGE:")
    coverage = await conn.fetch("""
        SELECT 
            pp.name as plant_part,
            COUNT(up.id) as product_count,
            COUNT(DISTINCT up.industry_sub_category_id) as industry_count,
            COALESCE(SUM(CASE WHEN up.created_at > NOW() - INTERVAL '7 days' THEN 1 ELSE 0 END), 0) as recent_additions
        FROM plant_parts pp
        LEFT JOIN uses_products up ON pp.id = up.plant_part_id
        GROUP BY pp.id, pp.name
        ORDER BY product_count DESC
    """)
    
    for part in coverage:
        status = "🟢" if part['product_count'] > 50 else "🟡" if part['product_count'] > 10 else "🔴"
        recent = f" (+{part['recent_additions']})" if part['recent_additions'] > 0 else ""
        print(f"   {status} {part['plant_part']}: {part['product_count']} products{recent}")
    
    # 4. Deduplication Status
    print("\n\n🔍 DEDUPLICATION STATUS:")
    dup_stats = await conn.fetchrow("""
        SELECT 
            COUNT(*) FILTER (WHERE canonical_product_id IS NOT NULL) as merged_duplicates,
            COUNT(*) FILTER (WHERE verification_status = 'ai_verified') as ai_verified,
            COUNT(*) FILTER (WHERE verification_status = 'human_verified') as human_verified,
            COUNT(*) FILTER (WHERE source_url IS NOT NULL) as with_sources
        FROM uses_products
    """)
    
    total_products = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
    
    print(f"   Merged Duplicates: {dup_stats['merged_duplicates']}")
    print(f"   AI Verified: {dup_stats['ai_verified']} ({dup_stats['ai_verified']/total_products*100:.1f}%)")
    print(f"   Human Verified: {dup_stats['human_verified']} ({dup_stats['human_verified']/total_products*100:.1f}%)")
    print(f"   With Sources: {dup_stats['with_sources']} ({dup_stats['with_sources']/total_products*100:.1f}%)")
    
    # 5. Next Actions
    print("\n\n🎯 RECOMMENDED ACTIONS:")
    
    # Find plant parts needing attention
    low_coverage = await conn.fetch("""
        SELECT pp.name, COUNT(up.id) as count
        FROM plant_parts pp
        LEFT JOIN uses_products up ON pp.id = up.plant_part_id
        GROUP BY pp.id, pp.name
        HAVING COUNT(up.id) < 20
        ORDER BY COUNT(up.id)
    """)
    
    if low_coverage:
        print("   Priority Plant Parts:")
        for part in low_coverage:
            print(f"   - {part['name']}: {part['count']} products (needs attention)")
    
    # Check for stale data
    stale_check = await conn.fetchval("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE verification_status = 'unverified' 
        AND created_at < NOW() - INTERVAL '30 days'
    """)
    
    if stale_check > 0:
        print(f"\n   ⚠️  {stale_check} products need verification (>30 days old)")
    
    # Save dashboard data
    dashboard_data = {
        'generated_at': datetime.now().isoformat(),
        'total_products': total_products,
        'agent_activity': [dict(row) for row in agent_stats] if agent_stats else [],
        'growth_data': [{'date': str(row['date']), 'added': row['products_added']} for row in growth_data],
        'plant_coverage': [{'name': row['plant_part'], 'count': row['product_count']} for row in coverage]
    }
    
    with open('agent_dashboard.json', 'w') as f:
        json.dump(dashboard_data, f, indent=2, default=str)
    
    print(f"\n\n💾 Dashboard data saved to agent_dashboard.json")
    print("="*60)
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(generate_dashboard())