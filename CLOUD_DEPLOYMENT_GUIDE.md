# Cloud Deployment Guide for Hemp Database Automation

## 🏆 Best Cost-Efficient Options (Ranked)

### 1. Oracle Cloud Free Tier (BEST - Always Free)
**Cost: $0/month forever**
- 2 AMD compute instances (1/8 OCPU, 1 GB RAM each)
- 200 GB storage
- 10 TB outbound data per month
- **Perfect for your automation needs**
- Never expires (truly free forever)

### 2. Google Cloud Free Tier (Good - 90 days)
**Cost: $0 for 90 days, then ~$5/month**
- $300 credit for 90 days
- e2-micro instance (0.25 vCPU, 1 GB RAM)
- 30 GB storage
- After trial: ~$5/month for small instance

### 3. AWS EC2 Free Tier (OK - 12 months)
**Cost: $0 for 12 months, then ~$8/month**
- t2.micro instance (1 vCPU, 1 GB RAM)
- 30 GB storage
- 750 hours/month (enough for 24/7)
- After trial: ~$8/month

### 4. Vultr/DigitalOcean (Simple)
**Cost: $4-6/month from start**
- 1 vCPU, 512MB-1GB RAM
- 10-25 GB SSD
- 1 TB transfer
- Very easy to use

## 🚀 Quick Setup: Oracle Cloud (Recommended)

### Step 1: Create Oracle Cloud Account
1. Go to: https://www.oracle.com/cloud/free/
2. Click "Start for free"
3. Sign up (needs credit card but won't charge)
4. Select region closest to you

### Step 2: Create VM Instance
1. Go to Compute → Instances
2. Click "Create Instance"
3. Use these settings:
   - Name: hemp-database-automation
   - Image: Ubuntu 22.04
   - Shape: VM.Standard.E2.1.Micro (Always Free)
   - Add SSH key (generate one)

### Step 3: Connect & Setup
```bash
# Connect to your instance
ssh ubuntu@<your-instance-ip>

# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3-pip python3-venv git postgresql-client -y

# Clone your project
git clone https://github.com/yourusername/HQz-Ai-DB-MCP-3.git
cd HQz-Ai-DB-MCP-3

# Create virtual environment
python3 -m venv venv_cloud
source venv_cloud/bin/activate

# Install requirements
pip install -r requirements.txt
pip install schedule

# Copy your .env file
nano .env  # Paste your environment variables
```

### Step 4: Setup Automation
```bash
# Create systemd service
sudo nano /etc/systemd/system/hemp-automation.service
```

Paste this:
```ini
[Unit]
Description=Hemp Database Automation
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/HQz-Ai-DB-MCP-3
Environment="PATH=/home/<USER>/HQz-Ai-DB-MCP-3/venv_cloud/bin"
ExecStart=/home/<USER>/HQz-Ai-DB-MCP-3/venv_cloud/bin/python start_automation.py --continuous
Restart=always
RestartSec=60

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl daemon-reload
sudo systemctl enable hemp-automation
sudo systemctl start hemp-automation
sudo systemctl status hemp-automation
```

## 💰 Cost Comparison

| Provider | Free Period | After Free | Storage | Best For |
|----------|------------|------------|---------|----------|
| Oracle | Forever | $0 | 200 GB | **Long-term projects** |
| Google | 90 days | ~$5/mo | 30 GB | Testing & development |
| AWS | 12 months | ~$8/mo | 30 GB | AWS ecosystem |
| Vultr | None | $4/mo | 10 GB | Simple, no surprises |

## 🔧 Migration Script

Save your current database state:
```bash
# On local machine
python export_database.py
```

This creates a backup you can import on the cloud server.

## 🚨 Important Security Steps

1. **Firewall Setup**:
```bash
sudo ufw allow 22  # SSH
sudo ufw enable
```

2. **Environment Variables**:
- Never commit .env to git
- Use cloud provider's secret management

3. **Regular Backups**:
```bash
# Add to crontab
0 3 * * * python backup_database.py
```

## 📊 Monitoring from Anywhere

Access your stats from any device:
```bash
# SSH to your server
ssh ubuntu@<your-ip>

# Check status
python monitor_automation.py

# View logs
tail -f logs/automation*.log
```

## 🎯 Next Steps

1. Choose Oracle Cloud (free forever)
2. I'll help you through the setup
3. Your automation runs 24/7
4. Access from anywhere
5. Zero monthly cost!