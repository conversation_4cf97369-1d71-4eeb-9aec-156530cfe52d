[Unit]
Description=Hemp Database AI Agent Orchestrator
After=network.target

[Service]
Type=simple
User=hempquarterz
WorkingDirectory=/home/<USER>/projects/HQz-Ai-DB-MCP-3
Environment="PATH=/home/<USER>/projects/HQz-Ai-DB-MCP-3/venv_dedup/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
Environment="PYTHONPATH=/home/<USER>/projects/HQz-Ai-DB-MCP-3"
ExecStart=/home/<USER>/projects/HQz-Ai-DB-MCP-3/venv_dedup/bin/python /home/<USER>/projects/HQz-Ai-DB-MCP-3/start_automation.py --continuous
Restart=always
RestartSec=60
StandardOutput=append:/home/<USER>/projects/HQz-Ai-DB-MCP-3/logs/systemd.log
StandardError=append:/home/<USER>/projects/HQz-Ai-DB-MCP-3/logs/systemd_error.log

[Install]
WantedBy=multi-user.target