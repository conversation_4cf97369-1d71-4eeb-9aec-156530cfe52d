#!/usr/bin/env python3
"""
Monitor automation status and agent performance
"""

import os
import json
import asyncio
import asyncpg
from datetime import datetime, timedelta
from pathlib import Path
from urllib.parse import urlparse, unquote
from dotenv import load_dotenv

load_dotenv()


async def check_automation_status():
    """Check the status of automated agents"""
    
    print("\n" + "="*70)
    print("🔍 AUTOMATION STATUS CHECK")
    print("="*70)
    print(f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check orchestrator state
    state_file = Path("orchestrator_state.json")
    if state_file.exists():
        with open(state_file, 'r') as f:
            state = json.load(f)
        
        print("\n📅 LAST RUN TIMES:")
        print("-" * 40)
        for agent, timestamp in state.get('last_runs', {}).items():
            last_run = datetime.fromisoformat(timestamp)
            time_ago = datetime.now() - last_run
            hours_ago = time_ago.total_seconds() / 3600
            
            if hours_ago < 1:
                status = "✅ Recently run"
            elif hours_ago < 24:
                status = f"🟡 {hours_ago:.1f} hours ago"
            else:
                status = f"🔴 {hours_ago/24:.1f} days ago"
                
            print(f"{agent:<20} {last_run.strftime('%Y-%m-%d %H:%M')}  {status}")
    else:
        print("\n⚠️  No orchestrator state found - automation may not be running")
    
    # Check recent agent activity in database
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        parsed = urlparse(database_url)
        
        try:
            conn = await asyncpg.connect(
                host=parsed.hostname,
                port=parsed.port or 5432,
                database=parsed.path[1:],
                user=parsed.username,
                password=unquote(parsed.password),
                ssl='require',
                statement_cache_size=0
            )
            
            # Products added in last 24 hours
            query = """
                SELECT source_agent, COUNT(*) as count, 
                       MAX(created_at) as last_added
                FROM uses_products
                WHERE created_at > $1
                GROUP BY source_agent
                ORDER BY last_added DESC
            """
            
            yesterday = datetime.now() - timedelta(hours=24)
            rows = await conn.fetch(query, yesterday)
            
            print("\n📊 LAST 24 HOURS ACTIVITY:")
            print("-" * 40)
            
            if rows:
                for row in rows:
                    print(f"{row['source_agent']:<25} {row['count']:>3} products   Last: {row['last_added'].strftime('%H:%M')}")
            else:
                print("No products added in last 24 hours")
            
            # Total products
            total = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
            ai_total = await conn.fetchval(
                "SELECT COUNT(*) FROM uses_products WHERE source_agent = 'ai_discovery_agent'"
            )
            
            print(f"\n📈 DATABASE TOTALS:")
            print(f"Total products: {total}")
            print(f"AI-generated: {ai_total} ({ai_total/total*100:.1f}%)")
            
            await conn.close()
            
        except Exception as e:
            print(f"\n❌ Database error: {e}")
    
    # Check log files
    print("\n📁 LOG FILES:")
    print("-" * 40)
    
    log_dir = Path("logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("*.log"))
        if log_files:
            # Sort by modification time
            log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for log_file in log_files[:5]:  # Show last 5
                size = log_file.stat().st_size / 1024  # KB
                mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                age = datetime.now() - mtime
                
                if age.total_seconds() < 3600:
                    status = "🟢 Active"
                elif age.total_seconds() < 86400:
                    status = "🟡 Recent"
                else:
                    status = "⚪ Old"
                    
                print(f"{status} {log_file.name:<30} {size:>6.1f} KB   Modified: {mtime.strftime('%Y-%m-%d %H:%M')}")
        else:
            print("No log files found")
    else:
        print("Log directory not found")
    
    # Check processes
    print("\n🔄 RUNNING PROCESSES:")
    print("-" * 40)
    
    import subprocess
    try:
        # Check for python processes running our scripts
        result = subprocess.run(
            ["ps", "aux"], 
            capture_output=True, 
            text=True
        )
        
        our_processes = []
        for line in result.stdout.split('\n'):
            if 'python' in line and any(script in line for script in [
                'automated_orchestrator.py',
                'launch_enhanced_ai_agent.py',
                'simple_orchestrator.py'
            ]):
                our_processes.append(line)
        
        if our_processes:
            for proc in our_processes:
                parts = proc.split()
                if len(parts) > 10:
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    cmd = ' '.join(parts[10:])[:50] + '...'
                    print(f"PID: {pid:<8} CPU: {cpu:<5} MEM: {mem:<5} {cmd}")
        else:
            print("No orchestrator processes found running")
            
    except Exception as e:
        print(f"Could not check processes: {e}")
    
    print("\n" + "="*70)
    print("💡 QUICK ACTIONS:")
    print("-" * 40)
    print("1. Start automation:     python automated_orchestrator.py")
    print("2. Run single cycle:     python automated_orchestrator.py --cycle")
    print("3. Check logs:          tail -f logs/orchestrator_*.log")
    print("4. Setup automation:    ./setup_automation.sh")
    print("="*70)


if __name__ == "__main__":
    asyncio.run(check_automation_status())